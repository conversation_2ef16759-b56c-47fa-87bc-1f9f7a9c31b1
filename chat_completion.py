"""
TogetherAI Integration Module for RAG Pipeline
Handles chat completion with context injection and API management
"""

import os
import logging
from typing import List, Dict, Optional, Any
from together import Together
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TogetherAIClient:
    """Manages TogetherAI API interactions"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('TOGETHERAI_API_KEY')
        if not self.api_key:
            raise ValueError("TogetherAI API key not found. Set TOGETHERAI_API_KEY environment variable.")
        
        self.client = Together(api_key=self.api_key)
        self.default_model = "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"
        self.connection_status = False
        
        # Test connection
        self._test_connection()
    
    def _test_connection(self):
        """Test API connection"""
        try:
            logger.info("Testing TogetherAI API connection...")
            response = self.client.chat.completions.create(
                model=self.default_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            self.connection_status = True
            logger.info("✅ Successfully connected to TogetherAI API")
        except Exception as e:
            self.connection_status = False
            logger.error(f"❌ Failed to connect to TogetherAI API: {e}")
            raise
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        try:
            models = self.client.models.list()
            return [model.id for model in models.data if 'chat' in model.type.lower()]
        except Exception as e:
            logger.error(f"Error fetching models: {e}")
            return [self.default_model]
    
    def generate_response(self, 
                         messages: List[Dict[str, str]], 
                         model: Optional[str] = None,
                         max_tokens: int = 1000,
                         temperature: float = 0.7,
                         stream: bool = False) -> Dict[str, Any]:
        """
        Generate chat completion response
        
        Args:
            messages: List of message dictionaries
            model: Model to use (defaults to default_model)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            stream: Whether to stream response
        
        Returns:
            Response dictionary with content and metadata
        """
        if not self.connection_status:
            raise RuntimeError("TogetherAI API not connected")
        
        model = model or self.default_model
        
        try:
            start_time = time.time()
            
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream
            )
            
            end_time = time.time()
            
            if stream:
                return {
                    'success': True,
                    'response': response,
                    'model': model,
                    'stream': True
                }
            else:
                content = response.choices[0].message.content
                
                return {
                    'success': True,
                    'content': content,
                    'model': model,
                    'usage': {
                        'prompt_tokens': response.usage.prompt_tokens if response.usage else 0,
                        'completion_tokens': response.usage.completion_tokens if response.usage else 0,
                        'total_tokens': response.usage.total_tokens if response.usage else 0
                    },
                    'response_time': end_time - start_time,
                    'stream': False
                }
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return {
                'success': False,
                'error': str(e),
                'model': model
            }

class RAGChatbot:
    """RAG-enabled chatbot using TogetherAI"""
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 model: Optional[str] = None,
                 system_prompt: Optional[str] = None):
        
        self.together_client = TogetherAIClient(api_key)
        self.model = model or self.together_client.default_model
        
        self.default_system_prompt = """You are a helpful AI assistant that answers questions based on the provided context. 

Instructions:
1. Use the context provided to answer the user's question accurately
2. If the context doesn't contain enough information to answer the question, say so clearly
3. Cite specific parts of the context when possible
4. Be concise but comprehensive in your responses
5. If asked about something not in the context, acknowledge the limitation

Context will be provided in the format: [Context N] (Source: filename, Similarity: score)"""
        
        self.system_prompt = system_prompt or self.default_system_prompt
        self.conversation_history = []
    
    def generate_rag_response(self, 
                             user_query: str, 
                             context: str,
                             include_history: bool = True,
                             max_tokens: int = 1000,
                             temperature: float = 0.7) -> Dict[str, Any]:
        """
        Generate RAG response with context
        
        Args:
            user_query: User's question
            context: Retrieved context
            include_history: Whether to include conversation history
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
        
        Returns:
            Response dictionary
        """
        try:
            # Prepare messages
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history if requested
            if include_history and self.conversation_history:
                messages.extend(self.conversation_history[-6:])  # Last 3 exchanges
            
            # Add context and user query
            if context.strip():
                context_message = f"Context:\n{context}\n\nUser Question: {user_query}"
            else:
                context_message = f"No relevant context found.\n\nUser Question: {user_query}"
            
            messages.append({"role": "user", "content": context_message})
            
            # Generate response
            response = self.together_client.generate_response(
                messages=messages,
                model=self.model,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            if response['success']:
                # Update conversation history
                self.conversation_history.append({"role": "user", "content": user_query})
                self.conversation_history.append({"role": "assistant", "content": response['content']})
                
                # Keep history manageable
                if len(self.conversation_history) > 20:
                    self.conversation_history = self.conversation_history[-20:]
                
                logger.info(f"Generated RAG response for query: {user_query[:50]}...")
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating RAG response: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def chat_without_context(self, 
                           user_query: str,
                           max_tokens: int = 1000,
                           temperature: float = 0.7) -> Dict[str, Any]:
        """Generate response without RAG context"""
        try:
            messages = [
                {"role": "system", "content": "You are a helpful AI assistant."},
                {"role": "user", "content": user_query}
            ]
            
            return self.together_client.generate_response(
                messages=messages,
                model=self.model,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
        except Exception as e:
            logger.error(f"Error in chat without context: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history = []
        logger.info("Conversation history cleared")
    
    def get_conversation_history(self) -> List[Dict[str, str]]:
        """Get current conversation history"""
        return self.conversation_history.copy()
    
    def set_system_prompt(self, prompt: str):
        """Update system prompt"""
        self.system_prompt = prompt
        logger.info("System prompt updated")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about current model and connection"""
        return {
            'model': self.model,
            'connection_status': self.together_client.connection_status,
            'api_key_set': bool(self.together_client.api_key),
            'conversation_length': len(self.conversation_history)
        }

class StreamingRAGChatbot(RAGChatbot):
    """RAG Chatbot with streaming support"""
    
    def generate_rag_response_stream(self, 
                                   user_query: str, 
                                   context: str,
                                   include_history: bool = True,
                                   max_tokens: int = 1000,
                                   temperature: float = 0.7):
        """
        Generate streaming RAG response
        
        Args:
            user_query: User's question
            context: Retrieved context
            include_history: Whether to include conversation history
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
        
        Yields:
            Response chunks
        """
        try:
            # Prepare messages (same as non-streaming)
            messages = [{"role": "system", "content": self.system_prompt}]
            
            if include_history and self.conversation_history:
                messages.extend(self.conversation_history[-6:])
            
            if context.strip():
                context_message = f"Context:\n{context}\n\nUser Question: {user_query}"
            else:
                context_message = f"No relevant context found.\n\nUser Question: {user_query}"
            
            messages.append({"role": "user", "content": context_message})
            
            # Generate streaming response
            response = self.together_client.generate_response(
                messages=messages,
                model=self.model,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True
            )
            
            if response['success']:
                full_content = ""
                for chunk in response['response']:
                    if chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        full_content += content
                        yield content
                
                # Update conversation history after streaming is complete
                self.conversation_history.append({"role": "user", "content": user_query})
                self.conversation_history.append({"role": "assistant", "content": full_content})
                
                if len(self.conversation_history) > 20:
                    self.conversation_history = self.conversation_history[-20:]
            else:
                yield f"Error: {response.get('error', 'Unknown error')}"
                
        except Exception as e:
            logger.error(f"Error in streaming response: {e}")
            yield f"Error: {str(e)}"

# Convenience functions
def quick_chat(query: str, context: str = "", api_key: Optional[str] = None) -> str:
    """Quick chat function for simple use cases"""
    chatbot = RAGChatbot(api_key=api_key)
    response = chatbot.generate_rag_response(query, context)
    
    if response['success']:
        return response['content']
    else:
        return f"Error: {response.get('error', 'Unknown error')}"

def test_together_connection(api_key: Optional[str] = None) -> bool:
    """Test TogetherAI connection"""
    try:
        client = TogetherAIClient(api_key)
        return client.connection_status
    except Exception:
        return False
