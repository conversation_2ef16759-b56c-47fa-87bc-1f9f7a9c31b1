"""
Test suite for chat completion module
"""

import pytest
import os
from unittest.mock import <PERSON><PERSON>, patch, MagicMock
from chat_completion import (
    TogetherAIClient,
    RAGChatbot,
    StreamingRAGChatbot,
    quick_chat,
    test_together_connection
)

class TestTogetherAIClient:
    """Test TogetherAI client functionality"""
    
    @patch.dict(os.environ, {'TOGETHERAI_API_KEY': 'test_key'})
    @patch('chat_completion.Together')
    def test_initialization_with_env_key(self, mock_together):
        """Test client initialization with environment variable"""
        # Mock Together client
        mock_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Hello"
        mock_client.chat.completions.create.return_value = mock_response
        mock_together.return_value = mock_client
        
        client = TogetherAIClient()
        
        assert client.api_key == 'test_key'
        assert client.connection_status is True
    
    @patch('chat_completion.Together')
    def test_initialization_with_explicit_key(self, mock_together):
        """Test client initialization with explicit API key"""
        # Mock Together client
        mock_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Hello"
        mock_client.chat.completions.create.return_value = mock_response
        mock_together.return_value = mock_client
        
        client = TogetherAIClient(api_key='explicit_key')
        
        assert client.api_key == 'explicit_key'
        assert client.connection_status is True
    
    def test_initialization_without_key(self):
        """Test client initialization without API key"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="TogetherAI API key not found"):
                TogetherAIClient()
    
    @patch('chat_completion.Together')
    def test_connection_test_failure(self, mock_together):
        """Test connection test failure"""
        # Mock Together client to raise exception
        mock_client = Mock()
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        mock_together.return_value = mock_client
        
        with pytest.raises(Exception):
            TogetherAIClient(api_key='test_key')
    
    @patch('chat_completion.Together')
    def test_generate_response_success(self, mock_together):
        """Test successful response generation"""
        # Mock Together client
        mock_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Test response"
        mock_response.usage = Mock()
        mock_response.usage.prompt_tokens = 10
        mock_response.usage.completion_tokens = 20
        mock_response.usage.total_tokens = 30
        mock_client.chat.completions.create.return_value = mock_response
        mock_together.return_value = mock_client
        
        client = TogetherAIClient(api_key='test_key')
        
        messages = [{"role": "user", "content": "Hello"}]
        result = client.generate_response(messages)
        
        assert result['success'] is True
        assert result['content'] == "Test response"
        assert result['usage']['prompt_tokens'] == 10
        assert result['usage']['completion_tokens'] == 20
        assert result['usage']['total_tokens'] == 30
        assert 'response_time' in result
    
    @patch('chat_completion.Together')
    def test_generate_response_error(self, mock_together):
        """Test response generation error handling"""
        # Mock Together client
        mock_client = Mock()
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        mock_together.return_value = mock_client
        
        # Mock successful connection test
        with patch.object(TogetherAIClient, '_test_connection'):
            client = TogetherAIClient(api_key='test_key')
            client.connection_status = True
            
            messages = [{"role": "user", "content": "Hello"}]
            result = client.generate_response(messages)
            
            assert result['success'] is False
            assert 'error' in result
    
    @patch('chat_completion.Together')
    def test_generate_response_streaming(self, mock_together):
        """Test streaming response generation"""
        # Mock Together client
        mock_client = Mock()
        mock_response = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        mock_together.return_value = mock_client
        
        # Mock successful connection test
        with patch.object(TogetherAIClient, '_test_connection'):
            client = TogetherAIClient(api_key='test_key')
            client.connection_status = True
            
            messages = [{"role": "user", "content": "Hello"}]
            result = client.generate_response(messages, stream=True)
            
            assert result['success'] is True
            assert result['stream'] is True
            assert 'response' in result

class TestRAGChatbot:
    """Test RAG chatbot functionality"""
    
    @patch('chat_completion.TogetherAIClient')
    def setup_method(self, mock_together_client):
        """Setup with mocked TogetherAI client"""
        # Mock TogetherAI client
        mock_client_instance = Mock()
        mock_client_instance.connection_status = True
        mock_client_instance.default_model = "test-model"
        mock_client_instance.generate_response.return_value = {
            'success': True,
            'content': 'Test response',
            'usage': {'total_tokens': 100}
        }
        mock_together_client.return_value = mock_client_instance
        
        self.chatbot = RAGChatbot(api_key='test_key')
    
    def test_initialization(self):
        """Test chatbot initialization"""
        assert self.chatbot.together_client is not None
        assert self.chatbot.model == "test-model"
        assert self.chatbot.system_prompt is not None
        assert self.chatbot.conversation_history == []
    
    def test_generate_rag_response_success(self):
        """Test successful RAG response generation"""
        query = "What is machine learning?"
        context = "Machine learning is a subset of AI..."
        
        result = self.chatbot.generate_rag_response(query, context)
        
        assert result['success'] is True
        assert result['content'] == 'Test response'
        assert len(self.chatbot.conversation_history) == 2  # User + assistant
    
    def test_generate_rag_response_no_context(self):
        """Test RAG response generation without context"""
        query = "What is machine learning?"
        context = ""
        
        result = self.chatbot.generate_rag_response(query, context)
        
        assert result['success'] is True
        assert result['content'] == 'Test response'
    
    def test_generate_rag_response_with_history(self):
        """Test RAG response generation with conversation history"""
        # Add some history
        self.chatbot.conversation_history = [
            {"role": "user", "content": "Previous question"},
            {"role": "assistant", "content": "Previous answer"}
        ]
        
        query = "Follow-up question"
        context = "Some context"
        
        result = self.chatbot.generate_rag_response(
            query, 
            context, 
            include_history=True
        )
        
        assert result['success'] is True
        assert len(self.chatbot.conversation_history) == 4  # Previous + new
    
    def test_chat_without_context(self):
        """Test chat without RAG context"""
        query = "Hello, how are you?"
        
        result = self.chatbot.chat_without_context(query)
        
        assert result['success'] is True
        assert result['content'] == 'Test response'
    
    def test_clear_history(self):
        """Test clearing conversation history"""
        # Add some history
        self.chatbot.conversation_history = [
            {"role": "user", "content": "Test"},
            {"role": "assistant", "content": "Response"}
        ]
        
        self.chatbot.clear_history()
        
        assert self.chatbot.conversation_history == []
    
    def test_get_conversation_history(self):
        """Test getting conversation history"""
        # Add some history
        history = [
            {"role": "user", "content": "Test"},
            {"role": "assistant", "content": "Response"}
        ]
        self.chatbot.conversation_history = history
        
        retrieved_history = self.chatbot.get_conversation_history()
        
        assert retrieved_history == history
        assert retrieved_history is not self.chatbot.conversation_history  # Should be a copy
    
    def test_set_system_prompt(self):
        """Test setting custom system prompt"""
        new_prompt = "You are a specialized assistant."
        
        self.chatbot.set_system_prompt(new_prompt)
        
        assert self.chatbot.system_prompt == new_prompt
    
    def test_get_model_info(self):
        """Test getting model information"""
        info = self.chatbot.get_model_info()
        
        assert 'model' in info
        assert 'connection_status' in info
        assert 'api_key_set' in info
        assert 'conversation_length' in info
        assert info['model'] == "test-model"
        assert info['connection_status'] is True
        assert info['api_key_set'] is True
    
    def test_conversation_history_limit(self):
        """Test conversation history length limit"""
        # Add many messages to test history limit
        for i in range(25):
            self.chatbot.conversation_history.append({"role": "user", "content": f"Message {i}"})
        
        # Generate a response to trigger history cleanup
        result = self.chatbot.generate_rag_response("Test", "Context")
        
        # History should be limited to 20 messages
        assert len(self.chatbot.conversation_history) <= 22  # 20 + new user + new assistant

class TestStreamingRAGChatbot:
    """Test streaming RAG chatbot functionality"""
    
    @patch('chat_completion.TogetherAIClient')
    def setup_method(self, mock_together_client):
        """Setup with mocked TogetherAI client"""
        # Mock TogetherAI client
        mock_client_instance = Mock()
        mock_client_instance.connection_status = True
        mock_client_instance.default_model = "test-model"
        
        # Mock streaming response
        mock_chunk1 = Mock()
        mock_chunk1.choices = [Mock()]
        mock_chunk1.choices[0].delta.content = "Hello "
        
        mock_chunk2 = Mock()
        mock_chunk2.choices = [Mock()]
        mock_chunk2.choices[0].delta.content = "world!"
        
        mock_client_instance.generate_response.return_value = {
            'success': True,
            'response': [mock_chunk1, mock_chunk2]
        }
        mock_together_client.return_value = mock_client_instance
        
        self.streaming_chatbot = StreamingRAGChatbot(api_key='test_key')
    
    def test_generate_rag_response_stream(self):
        """Test streaming RAG response generation"""
        query = "What is AI?"
        context = "AI is artificial intelligence..."
        
        chunks = list(self.streaming_chatbot.generate_rag_response_stream(query, context))
        
        assert len(chunks) == 2
        assert chunks[0] == "Hello "
        assert chunks[1] == "world!"
        
        # Check that history was updated
        assert len(self.streaming_chatbot.conversation_history) == 2
        assert self.streaming_chatbot.conversation_history[-1]['content'] == "Hello world!"

class TestConvenienceFunctions:
    """Test convenience functions"""
    
    @patch('chat_completion.RAGChatbot')
    def test_quick_chat_success(self, mock_rag_chatbot):
        """Test quick chat function success"""
        # Mock RAGChatbot
        mock_instance = Mock()
        mock_instance.generate_rag_response.return_value = {
            'success': True,
            'content': 'Quick response'
        }
        mock_rag_chatbot.return_value = mock_instance
        
        result = quick_chat("Test query", "Test context")
        
        assert result == 'Quick response'
    
    @patch('chat_completion.RAGChatbot')
    def test_quick_chat_error(self, mock_rag_chatbot):
        """Test quick chat function error handling"""
        # Mock RAGChatbot
        mock_instance = Mock()
        mock_instance.generate_rag_response.return_value = {
            'success': False,
            'error': 'Test error'
        }
        mock_rag_chatbot.return_value = mock_instance
        
        result = quick_chat("Test query", "Test context")
        
        assert "Error: Test error" in result
    
    @patch('chat_completion.TogetherAIClient')
    def test_test_together_connection_success(self, mock_together_client):
        """Test connection test function success"""
        # Mock successful connection
        mock_instance = Mock()
        mock_instance.connection_status = True
        mock_together_client.return_value = mock_instance
        
        result = test_together_connection('test_key')
        
        assert result is True
    
    @patch('chat_completion.TogetherAIClient')
    def test_test_together_connection_failure(self, mock_together_client):
        """Test connection test function failure"""
        # Mock failed connection
        mock_together_client.side_effect = Exception("Connection failed")
        
        result = test_together_connection('test_key')
        
        assert result is False

class TestErrorHandling:
    """Test error handling scenarios"""
    
    @patch('chat_completion.TogetherAIClient')
    def test_rag_response_generation_error(self, mock_together_client):
        """Test RAG response generation error handling"""
        # Mock TogetherAI client with error
        mock_client_instance = Mock()
        mock_client_instance.connection_status = True
        mock_client_instance.default_model = "test-model"
        mock_client_instance.generate_response.side_effect = Exception("API Error")
        mock_together_client.return_value = mock_client_instance
        
        chatbot = RAGChatbot(api_key='test_key')
        result = chatbot.generate_rag_response("Test query", "Test context")
        
        assert result['success'] is False
        assert 'error' in result
    
    @patch('chat_completion.TogetherAIClient')
    def test_streaming_response_error(self, mock_together_client):
        """Test streaming response error handling"""
        # Mock TogetherAI client with error
        mock_client_instance = Mock()
        mock_client_instance.connection_status = True
        mock_client_instance.default_model = "test-model"
        mock_client_instance.generate_response.side_effect = Exception("Streaming Error")
        mock_together_client.return_value = mock_client_instance
        
        streaming_chatbot = StreamingRAGChatbot(api_key='test_key')
        chunks = list(streaming_chatbot.generate_rag_response_stream("Test", "Context"))
        
        assert len(chunks) == 1
        assert "Error:" in chunks[0]

if __name__ == "__main__":
    pytest.main([__file__])
