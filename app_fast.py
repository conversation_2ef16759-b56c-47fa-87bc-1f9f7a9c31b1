"""
Fast-Loading Streamlit RAG Application
Optimized for quick startup with lazy loading
"""

import streamlit as st
import os
import tempfile
import time
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="RAG Pipeline - PDF Chat Assistant (Fast)",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-box {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .status-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .status-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .status-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize Streamlit session state variables"""
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    if 'documents_ingested' not in st.session_state:
        st.session_state.documents_ingested = []
    
    if 'api_key_status' not in st.session_state:
        st.session_state.api_key_status = None

def lazy_load_ingestion():
    """Lazy load document ingestion system"""
    if 'ingestion_system' not in st.session_state:
        with st.spinner("🔄 Loading document processing system..."):
            from ingestion import DocumentIngestion
            st.session_state.ingestion_system = DocumentIngestion()
            st.success("✅ Document processing system loaded!")
    return st.session_state.ingestion_system

def lazy_load_retrieval():
    """Lazy load retrieval system"""
    if 'retrieval_system' not in st.session_state:
        with st.spinner("🔄 Loading retrieval system..."):
            from retrieval import RAGRetriever
            st.session_state.retrieval_system = RAGRetriever()
            st.success("✅ Retrieval system loaded!")
    return st.session_state.retrieval_system

def lazy_load_chatbot():
    """Lazy load chatbot system"""
    if 'chatbot' not in st.session_state:
        with st.spinner("🔄 Loading chat system..."):
            from chat_completion import RAGChatbot
            api_key = os.getenv('TOGETHERAI_API_KEY')
            if api_key:
                st.session_state.chatbot = RAGChatbot(api_key=api_key)
                st.success("✅ Chat system loaded!")
            else:
                st.error("❌ TogetherAI API key not found!")
                st.session_state.chatbot = None
    return st.session_state.chatbot

def estimate_tokens(text: str) -> int:
    """Simple token estimation (roughly 4 characters per token)"""
    return len(text) // 4

def check_api_status():
    """Quick API key check without connection test"""
    api_key = os.getenv('TOGETHERAI_API_KEY')
    if api_key and api_key != 'your_api_key_here':
        st.session_state.api_key_status = "✅ API Key Found"
        return True
    else:
        st.session_state.api_key_status = "❌ API Key Missing"
        return False

def display_system_status():
    """Display system status in sidebar"""
    st.sidebar.markdown("## 🔧 System Status")
    
    # API Key Status
    api_status = check_api_status()
    if api_status:
        st.sidebar.markdown("**API Connection:** ✅ Ready")
    else:
        st.sidebar.markdown("**API Connection:** ❌ Not configured")
    
    # Component Status
    components = {
        "Document Processing": 'ingestion_system' in st.session_state,
        "Vector Retrieval": 'retrieval_system' in st.session_state,
        "Chat System": 'chatbot' in st.session_state
    }
    
    st.sidebar.markdown("**Components:**")
    for name, loaded in components.items():
        status = "✅ Loaded" if loaded else "⏳ Not loaded"
        st.sidebar.markdown(f"- {name}: {status}")
    
    # Documents Status
    doc_count = len(st.session_state.documents_ingested)
    st.sidebar.markdown(f"**Documents:** {doc_count} processed")

def handle_pdf_upload():
    """Handle PDF file upload with lazy loading"""
    st.markdown("### 📄 Upload PDF Documents")
    
    uploaded_files = st.file_uploader(
        "Choose PDF files",
        type="pdf",
        accept_multiple_files=True,
        help="Upload one or more PDF files to process"
    )
    
    if uploaded_files:
        # Lazy load ingestion system only when needed
        ingestion_system = lazy_load_ingestion()
        
        col1, col2 = st.columns(2)
        
        with col1:
            extraction_method = st.selectbox(
                "Text Extraction Method",
                ["pdfplumber", "pypdf2"],
                help="Choose the PDF text extraction method"
            )
        
        with col2:
            chunking_method = st.selectbox(
                "Text Chunking Method",
                ["recursive", "character", "token"],
                help="Choose how to split the text into chunks"
            )
        
        if st.button("🚀 Process Documents", type="primary"):
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            for i, uploaded_file in enumerate(uploaded_files):
                try:
                    # Save uploaded file temporarily
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
                        tmp_file.write(uploaded_file.read())
                        tmp_path = tmp_file.name
                    
                    status_text.text(f"Processing {uploaded_file.name}...")
                    
                    # Process the PDF
                    result = ingestion_system.ingest_pdf(
                        tmp_path,
                        extraction_method=extraction_method,
                        chunking_method=chunking_method
                    )
                    
                    if result['success']:
                        estimated_tokens = estimate_tokens(str(result['total_characters']))
                        st.session_state.documents_ingested.append({
                            'filename': uploaded_file.name,
                            'chunks': result['chunks_created'],
                            'characters': result['total_characters'],
                            'estimated_tokens': estimated_tokens,
                            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
                        })

                        st.success(f"✅ {uploaded_file.name}: {result['chunks_created']} chunks, ~{estimated_tokens} tokens")
                    else:
                        st.error(f"❌ Failed to process {uploaded_file.name}: {result.get('error', 'Unknown error')}")
                    
                    # Clean up
                    os.unlink(tmp_path)
                    
                    # Update progress
                    progress_bar.progress((i + 1) / len(uploaded_files))
                
                except Exception as e:
                    st.error(f"❌ Error processing {uploaded_file.name}: {str(e)}")
            
            status_text.text("✅ Processing complete!")
            time.sleep(1)
            status_text.empty()
            progress_bar.empty()

def handle_chat_interface():
    """Handle chat interface with lazy loading"""
    st.markdown("### 💬 Chat with Your Documents")
    
    if not st.session_state.documents_ingested:
        st.warning("⚠️ Please upload and process some documents first!")
        return
    
    # Lazy load systems only when needed
    retrieval_system = lazy_load_retrieval()
    chatbot = lazy_load_chatbot()
    
    if not chatbot:
        st.error("❌ Chat system not available. Please check your API key.")
        return
    
    # Chat interface
    user_query = st.text_input(
        "Ask a question about your documents:",
        placeholder="What is the main topic discussed in the documents?"
    )
    
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        search_k = st.slider("Results to retrieve", 1, 10, 5)
    
    with col2:
        similarity_threshold = st.slider("Similarity threshold", 0.0, 1.0, 0.0, 0.1)
    
    if st.button("🔍 Ask Question", type="primary") and user_query:
        with st.spinner("🔍 Searching documents and generating response..."):
            try:
                # Retrieve relevant context
                search_results = retrieval_system.search(
                    user_query,
                    k=search_k,
                    similarity_threshold=similarity_threshold
                )
                
                if search_results['success'] and search_results['results']:
                    # Generate response
                    response = chatbot.generate_rag_response(
                        user_query,
                        search_results['context_text']
                    )
                    
                    if response['success']:
                        # Display response
                        st.markdown("#### 🤖 Response:")
                        st.markdown(response['content'])
                        
                        # Display sources
                        with st.expander("📚 Sources"):
                            for i, result in enumerate(search_results['results'][:3], 1):
                                st.markdown(f"**Source {i}** (Similarity: {result['similarity_score']:.3f})")
                                st.markdown(f"*File: {result['filename']}*")
                                st.markdown(result['chunk_text'][:300] + "...")
                                st.markdown("---")
                        
                        # Add to chat history
                        st.session_state.chat_history.append({
                            'query': user_query,
                            'response': response['content'],
                            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
                        })
                    else:
                        st.error(f"❌ Failed to generate response: {response.get('error', 'Unknown error')}")
                else:
                    st.warning("⚠️ No relevant documents found. Try adjusting your search parameters.")
            
            except Exception as e:
                st.error(f"❌ Error during search: {str(e)}")

def handle_chunks_viewer():
    """Display and explore document chunks"""
    st.markdown("### 🧩 Document Chunks Viewer")

    if not st.session_state.documents_ingested:
        st.warning("⚠️ No documents processed yet. Upload and process documents first!")
        return

    # Document selection
    doc_options = [f"{doc['filename']} ({doc['chunks']} chunks)" for doc in st.session_state.documents_ingested]
    selected_doc_idx = st.selectbox(
        "Select a document to view chunks:",
        range(len(doc_options)),
        format_func=lambda x: doc_options[x]
    )

    if selected_doc_idx is not None:
        selected_doc = st.session_state.documents_ingested[selected_doc_idx]

        # Display document info
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("📄 Filename", selected_doc['filename'])
        with col2:
            st.metric("🧩 Chunks", selected_doc['chunks'])
        with col3:
            st.metric("📝 Characters", f"{selected_doc['characters']:,}")
        with col4:
            st.metric("🕒 Processed", selected_doc['timestamp'])

        st.markdown("---")

        # Load retrieval system to access chunks
        try:
            retrieval_system = lazy_load_retrieval()

            # Get all chunks for this document
            st.markdown("### 📋 All Chunks")

            # Search controls
            col1, col2 = st.columns(2)
            with col1:
                search_term = st.text_input(
                    "🔍 Search within chunks:",
                    placeholder="Enter keywords to filter chunks..."
                )
            with col2:
                chunk_limit = st.slider("Max chunks to display", 5, 100, 20)

            # Get chunks from vector store
            if hasattr(retrieval_system, 'vector_store') and retrieval_system.vector_store:
                chunks_data = retrieval_system.vector_store.get_chunks_by_filename(selected_doc['filename'])

                if chunks_data:
                    # Filter chunks if search term provided
                    if search_term:
                        filtered_chunks = [
                            chunk for chunk in chunks_data
                            if search_term.lower() in chunk['text'].lower()
                        ]
                        st.info(f"Found {len(filtered_chunks)} chunks containing '{search_term}'")
                    else:
                        filtered_chunks = chunks_data

                    # Display chunks
                    chunks_to_show = filtered_chunks[:chunk_limit]

                    for i, chunk in enumerate(chunks_to_show, 1):
                        with st.expander(f"Chunk {chunk.get('chunk_id', i)}: {chunk['text'][:100]}..."):
                            st.markdown("**Full Text:**")
                            st.text_area(
                                f"Chunk {i} Content",
                                chunk['text'],
                                height=200,
                                key=f"chunk_{i}_{chunk.get('chunk_id', i)}"
                            )

                            # Chunk metadata
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric("📏 Length", f"{len(chunk['text'])} chars")
                            with col2:
                                st.metric("🔢 Tokens (est.)", estimate_tokens(chunk['text']))
                            with col3:
                                if 'embedding' in chunk:
                                    st.metric("🎯 Embedding Dim", len(chunk['embedding']))

                    if len(filtered_chunks) > chunk_limit:
                        st.info(f"Showing {chunk_limit} of {len(filtered_chunks)} chunks. Adjust the slider to see more.")

                else:
                    st.warning("⚠️ No chunks found for this document. The document might not be properly indexed.")

            else:
                st.error("❌ Vector store not available. Please process some documents first.")

        except Exception as e:
            st.error(f"❌ Error loading chunks: {str(e)}")
            st.info("💡 Try processing the document again or check the logs.")

def main():
    """Main application function"""
    # Initialize session state
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🚀 RAG Pipeline - Fast Mode</h1>', unsafe_allow_html=True)
    st.markdown("**Optimized for quick startup with lazy loading**")
    
    # Display system status
    display_system_status()
    
    # Main tabs
    tab1, tab2, tab3, tab4 = st.tabs(["📄 Document Upload", "💬 Chat Interface", "🧩 View Chunks", "📊 Chat History"])

    with tab1:
        handle_pdf_upload()

    with tab2:
        handle_chat_interface()

    with tab3:
        handle_chunks_viewer()

    with tab4:
        st.markdown("### 📊 Chat History")
        if st.session_state.chat_history:
            for i, chat in enumerate(reversed(st.session_state.chat_history), 1):
                with st.expander(f"Chat {len(st.session_state.chat_history) - i + 1}: {chat['query'][:50]}..."):
                    st.markdown(f"**Question:** {chat['query']}")
                    st.markdown(f"**Response:** {chat['response']}")
                    st.markdown(f"**Time:** {chat['timestamp']}")
        else:
            st.info("No chat history yet. Start a conversation in the Chat Interface tab!")

if __name__ == "__main__":
    main()
