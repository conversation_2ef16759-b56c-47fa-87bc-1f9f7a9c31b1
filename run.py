#!/usr/bin/env python3
"""
RAG Pipeline Runner
Provides multiple ways to run the application with different configurations
"""

import os
import sys
import argparse
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import streamlit
        import together
        import sentence_transformers
        import faiss
        import PyPDF2
        import pdfplumber
        logger.info("✅ All dependencies are installed")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.info("Please run: pip install -r requirements.txt")
        return False

def check_api_key():
    """Check if TogetherAI API key is set"""
    api_key = os.getenv('TOGETHERAI_API_KEY')
    if not api_key:
        logger.warning("⚠️ TOGETHERAI_API_KEY not found in environment variables")
        logger.info("Please set your API key in .env file or environment variables")
        return False
    
    logger.info("✅ TogetherAI API key found")
    return True

def run_streamlit(port=8501, host="localhost", auth=False):
    """Run the Streamlit application"""
    logger.info(f"🚀 Starting RAG Pipeline on http://{host}:{port}")
    
    # Set environment variables for Streamlit
    env = os.environ.copy()
    env['STREAMLIT_SERVER_PORT'] = str(port)
    env['STREAMLIT_SERVER_ADDRESS'] = host
    
    # Choose app file based on auth requirement
    app_file = "app_with_auth.py" if auth else "app.py"
    
    if auth and not os.path.exists(app_file):
        logger.info("Creating authenticated version of the app...")
        create_auth_app()
    
    # Run Streamlit
    cmd = [
        sys.executable, "-m", "streamlit", "run", app_file,
        "--server.port", str(port),
        "--server.address", host,
        "--server.headless", "true"
    ]
    
    try:
        subprocess.run(cmd, env=env)
    except KeyboardInterrupt:
        logger.info("🛑 Application stopped by user")
    except Exception as e:
        logger.error(f"❌ Error running application: {e}")

def create_auth_app():
    """Create an authenticated version of the app"""
    auth_app_content = '''
"""
Authenticated RAG Pipeline Application
"""

import streamlit as st
from auth import require_authentication

# Require authentication before accessing the app
if require_authentication():
    # Import and run the main app
    from app import main
    main()
'''
    
    with open("app_with_auth.py", "w") as f:
        f.write(auth_app_content)
    
    logger.info("✅ Created authenticated app version")

def run_tests():
    """Run the test suite"""
    logger.info("🧪 Running test suite...")
    
    try:
        import pytest
        result = pytest.main(["-v", "tests/"])
        return result == 0
    except ImportError:
        logger.error("❌ pytest not installed. Run: pip install pytest")
        return False

def setup_environment():
    """Set up the environment for first-time users"""
    logger.info("🔧 Setting up RAG Pipeline environment...")
    
    # Create .env file if it doesn't exist
    if not os.path.exists(".env"):
        logger.info("Creating .env file...")
        with open(".env", "w") as f:
            f.write("TOGETHERAI_API_KEY=your_api_key_here\n")
        logger.info("✅ Created .env file. Please add your TogetherAI API key.")
    
    # Create data directories
    os.makedirs("data", exist_ok=True)
    os.makedirs("tests", exist_ok=True)
    
    logger.info("✅ Environment setup complete!")

def run_docker():
    """Run the application using Docker"""
    logger.info("🐳 Running RAG Pipeline with Docker...")
    
    if not os.path.exists("Dockerfile"):
        logger.error("❌ Dockerfile not found")
        return False
    
    try:
        # Build Docker image
        logger.info("Building Docker image...")
        subprocess.run(["docker", "build", "-t", "rag-pipeline", "."], check=True)
        
        # Run Docker container
        logger.info("Starting Docker container...")
        subprocess.run([
            "docker", "run", "-p", "8501:8501",
            "--env-file", ".env",
            "-v", f"{os.getcwd()}/data:/app/data",
            "rag-pipeline"
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Docker error: {e}")
        return False
    except FileNotFoundError:
        logger.error("❌ Docker not found. Please install Docker.")
        return False

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="RAG Pipeline Runner")
    parser.add_argument("--port", type=int, default=8501, help="Port to run on (default: 8501)")
    parser.add_argument("--host", default="localhost", help="Host to bind to (default: localhost)")
    parser.add_argument("--auth", action="store_true", help="Enable authentication")
    parser.add_argument("--test", action="store_true", help="Run tests")
    parser.add_argument("--setup", action="store_true", help="Set up environment")
    parser.add_argument("--docker", action="store_true", help="Run with Docker")
    parser.add_argument("--check", action="store_true", help="Check dependencies and configuration")
    
    args = parser.parse_args()
    
    # Handle different commands
    if args.setup:
        setup_environment()
        return
    
    if args.test:
        if run_tests():
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ Some tests failed")
        return
    
    if args.check:
        logger.info("🔍 Checking system requirements...")
        deps_ok = check_dependencies()
        api_ok = check_api_key()
        
        if deps_ok and api_ok:
            logger.info("✅ System ready!")
        else:
            logger.error("❌ System not ready. Please fix the issues above.")
        return
    
    if args.docker:
        run_docker()
        return
    
    # Default: run the application
    if not check_dependencies():
        logger.error("❌ Dependencies not satisfied. Run with --setup first.")
        return
    
    if not check_api_key():
        logger.warning("⚠️ API key not set. Some features may not work.")
    
    run_streamlit(port=args.port, host=args.host, auth=args.auth)

if __name__ == "__main__":
    main()
