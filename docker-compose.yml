version: '3.8'

services:
  rag-pipeline:
    build: .
    ports:
      - "8501:8501"
    environment:
      - TOGETHERAI_API_KEY=${TOGETHERAI_API_KEY}
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
    volumes:
      - ./data:/app/data
      - ./faiss_index.index:/app/faiss_index.index
      - ./metadata.db:/app/metadata.db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - rag-pipeline
    restart: unless-stopped
    profiles:
      - production

volumes:
  data:
    driver: local
