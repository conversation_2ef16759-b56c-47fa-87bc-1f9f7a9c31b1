"""
Streamlit RAG Application
A complete Retrieval-Augmented Generation pipeline with PDF ingestion and chat interface
"""

import streamlit as st
import os
import tempfile
import time
from typing import Dict, List, Any
import logging

# Import our modules
from ingestion import DocumentIngestion
from retrieval import RAGRetriever
from chat_completion import RAGChatbot, test_together_connection

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="RAG Pipeline - PDF Chat Assistant",
    page_icon="📚",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-box {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .status-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .status-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .status-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
    .context-box {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize Streamlit session state variables"""
    if 'ingestion_system' not in st.session_state:
        st.session_state.ingestion_system = None
    
    if 'retrieval_system' not in st.session_state:
        st.session_state.retrieval_system = None
    
    if 'chatbot' not in st.session_state:
        st.session_state.chatbot = None
    
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    if 'api_connected' not in st.session_state:
        st.session_state.api_connected = False
    
    if 'documents_ingested' not in st.session_state:
        st.session_state.documents_ingested = []

def check_api_connection():
    """Check TogetherAI API connection"""
    try:
        st.session_state.api_connected = test_together_connection()
        return st.session_state.api_connected
    except Exception as e:
        st.session_state.api_connected = False
        return False

def initialize_systems():
    """Initialize ingestion and retrieval systems"""
    try:
        if st.session_state.ingestion_system is None:
            st.session_state.ingestion_system = DocumentIngestion(
                chunk_size=1000,
                chunk_overlap=200,
                embedding_model="sentence-transformers/all-MiniLM-L6-v2"
            )
        
        if st.session_state.retrieval_system is None:
            st.session_state.retrieval_system = RAGRetriever(
                embedding_model="sentence-transformers/all-MiniLM-L6-v2",
                use_hybrid=False,
                default_k=5
            )
        
        if st.session_state.chatbot is None and st.session_state.api_connected:
            st.session_state.chatbot = RAGChatbot()
        
        return True
    except Exception as e:
        st.error(f"Error initializing systems: {e}")
        return False

def display_connection_status():
    """Display API connection status"""
    if st.session_state.api_connected:
        st.markdown("""
        <div class="status-box status-success">
            ✅ <strong>Connected to TogetherAI API</strong><br>
            Model: meta-llama/Llama-3.3-70B-Instruct-Turbo-Free
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown("""
        <div class="status-box status-error">
            ❌ <strong>Not connected to TogetherAI API</strong><br>
            Please check your API key in the .env file
        </div>
        """, unsafe_allow_html=True)

def display_system_stats():
    """Display system statistics"""
    if st.session_state.ingestion_system:
        stats = st.session_state.ingestion_system.get_vector_store_stats()
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Documents", stats.get('total_files', 0))
        
        with col2:
            st.metric("Chunks", stats.get('total_chunks', 0))
        
        with col3:
            st.metric("Total Tokens", f"{stats.get('total_tokens', 0):,}")
        
        with col4:
            st.metric("Index Size", stats.get('index_size', 0))

def handle_pdf_upload():
    """Handle PDF file upload and ingestion"""
    st.subheader("📄 Document Ingestion")
    
    uploaded_files = st.file_uploader(
        "Upload PDF documents",
        type=['pdf'],
        accept_multiple_files=True,
        help="Upload one or more PDF files to add to the knowledge base"
    )
    
    if uploaded_files:
        col1, col2 = st.columns([3, 1])
        
        with col1:
            extraction_method = st.selectbox(
                "Text Extraction Method",
                ['pdfplumber', 'pypdf2'],
                help="pdfplumber is better for complex layouts"
            )
            
            chunking_method = st.selectbox(
                "Text Chunking Method",
                ['recursive', 'character', 'tokens'],
                help="recursive is recommended for most documents"
            )
        
        with col2:
            chunk_size = st.number_input(
                "Chunk Size",
                min_value=200,
                max_value=2000,
                value=1000,
                step=100
            )
            
            chunk_overlap = st.number_input(
                "Chunk Overlap",
                min_value=0,
                max_value=500,
                value=200,
                step=50
            )
        
        if st.button("🚀 Ingest Documents", type="primary"):
            if not st.session_state.ingestion_system:
                st.error("Ingestion system not initialized")
                return
            
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            for i, uploaded_file in enumerate(uploaded_files):
                status_text.text(f"Processing {uploaded_file.name}...")
                
                # Save uploaded file temporarily
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
                    tmp_file.write(uploaded_file.getvalue())
                    tmp_file_path = tmp_file.name
                
                try:
                    # Update chunking parameters
                    st.session_state.ingestion_system.text_chunker.chunk_size = chunk_size
                    st.session_state.ingestion_system.text_chunker.chunk_overlap = chunk_overlap
                    
                    # Ingest document
                    result = st.session_state.ingestion_system.ingest_pdf(
                        tmp_file_path,
                        extraction_method=extraction_method,
                        chunking_method=chunking_method
                    )
                    
                    if result['success']:
                        st.session_state.documents_ingested.append({
                            'filename': uploaded_file.name,
                            'chunks': result['chunks_created'],
                            'characters': result.get('total_characters', 0),
                            'method': extraction_method,
                            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
                        })

                        st.success(f"✅ {uploaded_file.name}: {result['chunks_created']} chunks created ({result.get('total_characters', 0)} characters)")
                    else:
                        st.error(f"❌ {uploaded_file.name}: {result['error']}")
                
                except Exception as e:
                    st.error(f"❌ Error processing {uploaded_file.name}: {e}")
                
                finally:
                    # Clean up temporary file
                    os.unlink(tmp_file_path)
                
                # Update progress
                progress_bar.progress((i + 1) / len(uploaded_files))
            
            status_text.text("✅ Ingestion complete!")
            st.rerun()

def handle_chat_interface():
    """Handle the chat interface"""
    st.subheader("💬 Chat with Your Documents")
    
    if not st.session_state.api_connected:
        st.warning("⚠️ TogetherAI API not connected. Please check your API key.")
        return
    
    if not st.session_state.chatbot:
        st.warning("⚠️ Chatbot not initialized.")
        return
    
    # Chat settings
    with st.expander("🔧 Chat Settings"):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            num_context_chunks = st.slider(
                "Context Chunks",
                min_value=1,
                max_value=10,
                value=5,
                help="Number of relevant chunks to retrieve"
            )
        
        with col2:
            temperature = st.slider(
                "Temperature",
                min_value=0.0,
                max_value=1.0,
                value=0.7,
                step=0.1,
                help="Controls randomness in responses"
            )
        
        with col3:
            max_tokens = st.slider(
                "Max Tokens",
                min_value=100,
                max_value=2000,
                value=1000,
                step=100,
                help="Maximum length of response"
            )
    
    # Display chat history
    for message in st.session_state.chat_history:
        with st.chat_message(message["role"]):
            st.write(message["content"])
            
            if message["role"] == "assistant" and "context" in message:
                with st.expander("📖 View Context Sources"):
                    for i, chunk in enumerate(message["context"], 1):
                        st.markdown(f"""
                        **Source {i}:** {chunk['filename']} 
                        (Similarity: {chunk['similarity_score']:.3f})
                        
                        {chunk['chunk_text'][:200]}...
                        """)
    
    # Chat input
    if prompt := st.chat_input("Ask a question about your documents..."):
        # Add user message to chat history
        st.session_state.chat_history.append({"role": "user", "content": prompt})
        
        with st.chat_message("user"):
            st.write(prompt)
        
        # Generate response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                try:
                    # Retrieve context
                    retrieval_result = st.session_state.retrieval_system.search(
                        prompt, 
                        k=num_context_chunks
                    )
                    
                    if retrieval_result['success']:
                        context_text = retrieval_result['context_text']
                        context_chunks = retrieval_result['results']
                        
                        # Generate response
                        response = st.session_state.chatbot.generate_rag_response(
                            prompt,
                            context_text,
                            max_tokens=max_tokens,
                            temperature=temperature
                        )
                        
                        if response['success']:
                            st.write(response['content'])
                            
                            # Add assistant message to chat history
                            st.session_state.chat_history.append({
                                "role": "assistant",
                                "content": response['content'],
                                "context": context_chunks
                            })
                            
                            # Show context sources
                            if context_chunks:
                                with st.expander("📖 View Context Sources"):
                                    for i, chunk in enumerate(context_chunks, 1):
                                        st.markdown(f"""
                                        **Source {i}:** {chunk['filename']} 
                                        (Similarity: {chunk['similarity_score']:.3f})
                                        
                                        {chunk['chunk_text'][:200]}...
                                        """)
                        else:
                            st.error(f"Error generating response: {response.get('error', 'Unknown error')}")
                    
                    else:
                        st.error(f"Error retrieving context: {retrieval_result.get('error', 'Unknown error')}")
                
                except Exception as e:
                    st.error(f"Error: {e}")

def estimate_tokens(text: str) -> int:
    """Simple token estimation (roughly 4 characters per token)"""
    return len(text) // 4

def handle_chunks_viewer():
    """Display and explore document chunks"""
    st.markdown("### 🧩 Document Chunks Viewer")

    # Debug information
    st.write(f"Debug: Session state documents_ingested: {len(st.session_state.documents_ingested)} documents")

    # Try to load documents from database if session state is empty
    if not st.session_state.documents_ingested and st.session_state.retrieval_system:
        try:
            # Get stats from vector store to see if there are any documents
            if hasattr(st.session_state.retrieval_system, 'vector_store') and st.session_state.retrieval_system.vector_store:
                stats = st.session_state.retrieval_system.vector_store.get_stats()
                st.write(f"Debug: Vector store stats: {stats}")

                if stats['total_files'] > 0:
                    st.info(f"Found {stats['total_files']} documents in database, but session state is empty. This might be due to app restart.")
                    # Try to reconstruct document list from database
                    try:
                        import sqlite3
                        conn = sqlite3.connect(st.session_state.retrieval_system.vector_store.db_path)
                        cursor = conn.cursor()
                        cursor.execute('SELECT DISTINCT filename, COUNT(*) as chunks, MIN(created_at) as timestamp FROM documents GROUP BY filename')

                        for row in cursor.fetchall():
                            st.session_state.documents_ingested.append({
                                'filename': row[0],
                                'chunks': row[1],
                                'characters': 'N/A',
                                'method': 'Unknown',
                                'timestamp': row[2]
                            })
                        conn.close()
                        st.success(f"Reconstructed {len(st.session_state.documents_ingested)} documents from database!")
                    except Exception as e:
                        st.error(f"Error reconstructing documents: {e}")
        except Exception as e:
            st.error(f"Error checking vector store: {e}")

    if not st.session_state.documents_ingested:
        st.warning("⚠️ No documents processed yet. Upload and process documents first!")
        return

    # Document selection
    doc_options = [f"{doc['filename']} ({doc['chunks']} chunks)" for doc in st.session_state.documents_ingested]
    selected_doc_idx = st.selectbox(
        "Select a document to view chunks:",
        range(len(doc_options)),
        format_func=lambda x: doc_options[x]
    )

    if selected_doc_idx is not None:
        selected_doc = st.session_state.documents_ingested[selected_doc_idx]

        # Display document info
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("📄 Filename", selected_doc['filename'])
        with col2:
            st.metric("🧩 Chunks", selected_doc['chunks'])
        with col3:
            st.metric("📝 Characters", f"{selected_doc.get('characters', 'N/A')}")
        with col4:
            st.metric("🕒 Processed", selected_doc['timestamp'])

        st.markdown("---")

        # Load retrieval system to access chunks
        try:
            if not st.session_state.retrieval_system:
                st.error("❌ Retrieval system not initialized. Please restart the application.")
                return

            # Get all chunks for this document
            st.markdown("### 📋 All Chunks")

            # Search controls
            col1, col2 = st.columns(2)
            with col1:
                search_term = st.text_input(
                    "🔍 Search within chunks:",
                    placeholder="Enter keywords to filter chunks..."
                )
            with col2:
                chunk_limit = st.slider("Max chunks to display", 5, 100, 20)

            # Get chunks from vector store
            if hasattr(st.session_state.retrieval_system, 'vector_store') and st.session_state.retrieval_system.vector_store:
                chunks_data = st.session_state.retrieval_system.vector_store.get_chunks_by_filename(selected_doc['filename'])

                if chunks_data:
                    # Filter chunks if search term provided
                    if search_term:
                        filtered_chunks = [
                            chunk for chunk in chunks_data
                            if search_term.lower() in chunk['text'].lower()
                        ]
                        st.info(f"Found {len(filtered_chunks)} chunks containing '{search_term}'")
                    else:
                        filtered_chunks = chunks_data

                    # Display chunks
                    chunks_to_show = filtered_chunks[:chunk_limit]

                    for i, chunk in enumerate(chunks_to_show, 1):
                        with st.expander(f"Chunk {chunk.get('chunk_id', i)}: {chunk['text'][:100]}..."):
                            st.markdown("**Full Text:**")
                            st.text_area(
                                f"Chunk {i} Content",
                                chunk['text'],
                                height=200,
                                key=f"chunk_{i}_{chunk.get('chunk_id', i)}"
                            )

                            # Chunk metadata
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric("📏 Length", f"{len(chunk['text'])} chars")
                            with col2:
                                st.metric("🔢 Tokens (est.)", estimate_tokens(chunk['text']))
                            with col3:
                                if 'tokens' in chunk:
                                    st.metric("🎯 Actual Tokens", chunk['tokens'])

                    if len(filtered_chunks) > chunk_limit:
                        st.info(f"Showing {chunk_limit} of {len(filtered_chunks)} chunks. Adjust the slider to see more.")

                else:
                    st.warning("⚠️ No chunks found for this document. The document might not be properly indexed.")

            else:
                st.error("❌ Vector store not available. Please process some documents first.")

        except Exception as e:
            st.error(f"❌ Error loading chunks: {str(e)}")
            st.info("💡 Try processing the document again or check the logs.")

def main():
    """Main application function"""
    # Initialize session state
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">📚 RAG Pipeline - PDF Chat Assistant</h1>', unsafe_allow_html=True)
    
    # Check API connection
    check_api_connection()
    
    # Initialize systems
    systems_ready = initialize_systems()
    
    # Sidebar
    with st.sidebar:
        st.header("🔧 System Status")
        display_connection_status()
        
        if systems_ready:
            st.markdown("""
            <div class="status-box status-success">
                ✅ <strong>Systems Initialized</strong><br>
                Embedding Model: all-MiniLM-L6-v2
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="status-box status-error">
                ❌ <strong>System Initialization Failed</strong>
            </div>
            """, unsafe_allow_html=True)
        
        st.header("📊 Statistics")
        display_system_stats()
        
        # Clear data button
        if st.button("🗑️ Clear All Data", type="secondary"):
            if st.session_state.ingestion_system:
                st.session_state.ingestion_system.clear_all_data()
                st.session_state.documents_ingested = []
                st.session_state.chat_history = []
                st.success("All data cleared!")
                st.rerun()
        
        # Show ingested documents
        if st.session_state.documents_ingested:
            st.header("📄 Ingested Documents")
            for doc in st.session_state.documents_ingested:
                st.text(f"📄 {doc['filename']}")
                st.text(f"   Chunks: {doc['chunks']}")
                st.text(f"   Time: {doc['timestamp']}")
    
    # Main content
    if not systems_ready:
        st.error("⚠️ Systems not ready. Please check the configuration.")
        return
    
    # Create tabs
    tab1, tab2, tab3 = st.tabs(["📄 Document Ingestion", "💬 Chat Interface", "🧩 View Chunks"])

    with tab1:
        handle_pdf_upload()

    with tab2:
        handle_chat_interface()

    with tab3:
        handle_chunks_viewer()

if __name__ == "__main__":
    main()
