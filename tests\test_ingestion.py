"""
Test suite for ingestion module
"""

import pytest
import tempfile
import os
import sqlite3
from unittest.mock import Mock, patch
import numpy as np

from ingestion import (
    PDFProcessor, 
    TextChunker, 
    EmbeddingGenerator, 
    VectorStore, 
    DocumentIngestion
)

class TestPDFProcessor:
    """Test PDF processing functionality"""
    
    def setup_method(self):
        self.processor = PDFProcessor()
    
    def test_supported_methods(self):
        """Test that supported methods are correctly defined"""
        assert 'pypdf2' in self.processor.supported_methods
        assert 'pdfplumber' in self.processor.supported_methods
    
    def test_extract_text_invalid_method(self):
        """Test error handling for invalid extraction method"""
        with pytest.raises(ValueError):
            self.processor.extract_text("dummy.pdf", "invalid_method")
    
    def test_extract_text_nonexistent_file(self):
        """Test error handling for nonexistent file"""
        with pytest.raises(FileNotFoundError):
            self.processor.extract_text("nonexistent.pdf")
    
    @patch('PyPDF2.PdfReader')
    def test_extract_text_pypdf2_success(self, mock_pdf_reader):
        """Test successful text extraction with PyPDF2"""
        # Mock PDF reader
        mock_page = Mock()
        mock_page.extract_text.return_value = "Sample text content"
        mock_pdf_reader.return_value.pages = [mock_page]
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            tmp_file.write(b"dummy pdf content")
            tmp_file_path = tmp_file.name
        
        try:
            result = self.processor.extract_text_pypdf2(tmp_file_path)
            assert "Sample text content" in result
            assert "Page 1" in result
        finally:
            os.unlink(tmp_file_path)

class TestTextChunker:
    """Test text chunking functionality"""
    
    def setup_method(self):
        self.chunker = TextChunker(chunk_size=100, chunk_overlap=20)
        self.sample_text = "This is a sample text. " * 20  # ~460 characters
    
    def test_initialization(self):
        """Test chunker initialization"""
        assert self.chunker.chunk_size == 100
        assert self.chunker.chunk_overlap == 20
        assert self.chunker.encoding is not None
    
    def test_count_tokens(self):
        """Test token counting"""
        text = "Hello world"
        token_count = self.chunker.count_tokens(text)
        assert isinstance(token_count, int)
        assert token_count > 0
    
    def test_chunk_by_character(self):
        """Test character-based chunking"""
        chunks = self.chunker.chunk_by_character(self.sample_text)
        assert len(chunks) > 1
        assert all(isinstance(chunk, str) for chunk in chunks)
        assert all(len(chunk) <= self.chunker.chunk_size + 50 for chunk in chunks)  # Allow some flexibility
    
    def test_chunk_recursive(self):
        """Test recursive chunking"""
        chunks = self.chunker.chunk_recursive(self.sample_text)
        assert len(chunks) > 1
        assert all(isinstance(chunk, str) for chunk in chunks)
    
    def test_chunk_by_tokens(self):
        """Test token-based chunking"""
        chunks = self.chunker.chunk_by_tokens(self.sample_text, max_tokens=50)
        assert len(chunks) > 1
        assert all(isinstance(chunk, str) for chunk in chunks)
    
    def test_chunk_text_invalid_method(self):
        """Test error handling for invalid chunking method"""
        with pytest.raises(ValueError):
            self.chunker.chunk_text(self.sample_text, "invalid_method")

class TestEmbeddingGenerator:
    """Test embedding generation"""
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_initialization(self, mock_sentence_transformer):
        """Test embedding generator initialization"""
        # Mock the model
        mock_model = Mock()
        mock_model.encode.return_value = np.array([[0.1, 0.2, 0.3]])
        mock_sentence_transformer.return_value = mock_model
        
        generator = EmbeddingGenerator("test-model")
        
        assert generator.model_name == "test-model"
        assert generator.model is not None
        assert generator.embedding_dim == 3
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_generate_embeddings(self, mock_sentence_transformer):
        """Test embedding generation"""
        # Mock the model
        mock_model = Mock()
        mock_embeddings = np.array([[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]])
        mock_model.encode.return_value = mock_embeddings
        mock_sentence_transformer.return_value = mock_model
        
        generator = EmbeddingGenerator("test-model")
        texts = ["text1", "text2"]
        
        embeddings = generator.generate_embeddings(texts)
        
        assert embeddings.shape == (2, 3)
        assert isinstance(embeddings, np.ndarray)

class TestVectorStore:
    """Test vector store functionality"""
    
    def setup_method(self):
        # Use temporary files for testing
        self.temp_dir = tempfile.mkdtemp()
        self.index_path = os.path.join(self.temp_dir, "test_index")
        self.db_path = os.path.join(self.temp_dir, "test.db")
        self.vector_store = VectorStore(self.index_path, self.db_path)
    
    def teardown_method(self):
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialization(self):
        """Test vector store initialization"""
        assert self.vector_store.index_path == self.index_path
        assert self.vector_store.db_path == self.db_path
        
        # Check database was created
        assert os.path.exists(self.db_path)
        
        # Check table exists
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='documents'")
        result = cursor.fetchone()
        conn.close()
        
        assert result is not None
    
    def test_create_index(self):
        """Test FAISS index creation"""
        self.vector_store.create_index(384)
        
        assert self.vector_store.index is not None
        assert self.vector_store.embedding_dim == 384
        assert self.vector_store.index.d == 384
    
    def test_add_documents(self):
        """Test adding documents to vector store"""
        # Create test data
        chunks = ["This is chunk 1", "This is chunk 2"]
        embeddings = np.random.rand(2, 384).astype(np.float32)
        
        # Create temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.pdf', delete=False) as tmp_file:
            tmp_file.write("test content")
            tmp_file_path = tmp_file.name
        
        try:
            start_id, end_id = self.vector_store.add_documents(tmp_file_path, chunks, embeddings)
            
            assert start_id == 0
            assert end_id == 1
            assert self.vector_store.index.ntotal == 2
            
            # Check database entries
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM documents")
            count = cursor.fetchone()[0]
            conn.close()
            
            assert count == 2
        
        finally:
            os.unlink(tmp_file_path)
    
    def test_search_empty_index(self):
        """Test search on empty index"""
        query_embedding = np.random.rand(1, 384).astype(np.float32)
        results = self.vector_store.search(query_embedding, k=5)
        
        assert results == []
    
    def test_get_stats(self):
        """Test getting vector store statistics"""
        stats = self.vector_store.get_stats()
        
        assert 'total_chunks' in stats
        assert 'total_files' in stats
        assert 'total_tokens' in stats
        assert 'index_size' in stats
        assert all(isinstance(v, int) for v in stats.values())

class TestDocumentIngestion:
    """Test complete document ingestion pipeline"""
    
    @patch('ingestion.EmbeddingGenerator')
    @patch('ingestion.VectorStore')
    def setup_method(self, mock_vector_store, mock_embedding_generator):
        """Setup with mocked dependencies"""
        # Mock embedding generator
        mock_embedding_instance = Mock()
        mock_embedding_instance.generate_embeddings.return_value = np.random.rand(5, 384).astype(np.float32)
        mock_embedding_generator.return_value = mock_embedding_instance
        
        # Mock vector store
        mock_vector_store_instance = Mock()
        mock_vector_store_instance.load_index.return_value = True
        mock_vector_store_instance.add_documents.return_value = (0, 4)
        mock_vector_store.return_value = mock_vector_store_instance
        
        self.ingestion = DocumentIngestion()
    
    def test_initialization(self):
        """Test document ingestion initialization"""
        assert self.ingestion.pdf_processor is not None
        assert self.ingestion.text_chunker is not None
        assert self.ingestion.embedding_generator is not None
        assert self.ingestion.vector_store is not None
    
    @patch('ingestion.PDFProcessor.extract_text')
    @patch('ingestion.TextChunker.chunk_text')
    def test_ingest_pdf_success(self, mock_chunk_text, mock_extract_text):
        """Test successful PDF ingestion"""
        # Mock PDF processing
        mock_extract_text.return_value = "Sample extracted text content"
        mock_chunk_text.return_value = ["chunk1", "chunk2", "chunk3"]
        
        # Create temporary PDF file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            tmp_file.write(b"dummy pdf content")
            tmp_file_path = tmp_file.name
        
        try:
            result = self.ingestion.ingest_pdf(tmp_file_path)
            
            assert result['success'] is True
            assert 'chunks_created' in result
            assert 'filename' in result
            assert result['chunks_created'] == 3
        
        finally:
            os.unlink(tmp_file_path)
    
    @patch('ingestion.PDFProcessor.extract_text')
    def test_ingest_pdf_no_text(self, mock_extract_text):
        """Test PDF ingestion with no extractable text"""
        mock_extract_text.return_value = ""
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            tmp_file.write(b"dummy pdf content")
            tmp_file_path = tmp_file.name
        
        try:
            result = self.ingestion.ingest_pdf(tmp_file_path)
            
            assert result['success'] is False
            assert 'error' in result
        
        finally:
            os.unlink(tmp_file_path)

# Integration tests
class TestIntegration:
    """Integration tests for the complete pipeline"""
    
    def test_end_to_end_pipeline(self):
        """Test the complete pipeline with minimal setup"""
        # This test would require actual models and files
        # For now, we'll just test that components can be instantiated together
        
        try:
            # Test that all components can be created
            pdf_processor = PDFProcessor()
            text_chunker = TextChunker(chunk_size=100, chunk_overlap=20)
            
            # These would require actual models in a real test
            # embedding_generator = EmbeddingGenerator()
            # vector_store = VectorStore()
            # ingestion = DocumentIngestion()
            
            assert pdf_processor is not None
            assert text_chunker is not None
            
        except Exception as e:
            pytest.fail(f"Component instantiation failed: {e}")

if __name__ == "__main__":
    pytest.main([__file__])
