"""
Test suite for retrieval module
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch
import numpy as np

from retrieval import (
    QueryProcessor,
    ContextRetriever,
    HybridRetriever,
    RAGRetriever,
    quick_search
)

class TestQueryProcessor:
    """Test query processing functionality"""
    
    def setup_method(self):
        self.processor = QueryProcessor()
    
    def test_preprocess_query(self):
        """Test query preprocessing"""
        # Test whitespace removal
        query = "  What is   machine learning?  "
        processed = self.processor.preprocess_query(query)
        assert processed == "What is machine learning?"
        
        # Test empty query
        empty_query = "   "
        processed_empty = self.processor.preprocess_query(empty_query)
        assert processed_empty == ""
    
    def test_expand_query(self):
        """Test query expansion"""
        # Test "how" expansion
        query = "How does machine learning work?"
        expanded = self.processor.expand_query(query)
        assert len(expanded) >= 1
        assert query in expanded
        
        # Test "what" expansion
        query = "What is deep learning?"
        expanded = self.processor.expand_query(query)
        assert len(expanded) >= 1
        assert query in expanded
        
        # Test no expansion
        query = "Machine learning concepts"
        expanded = self.processor.expand_query(query)
        assert expanded == [query]

class TestContextRetriever:
    """Test context retrieval functionality"""
    
    @patch('retrieval.EmbeddingGenerator')
    @patch('retrieval.VectorStore')
    def setup_method(self, mock_vector_store, mock_embedding_generator):
        """Setup with mocked dependencies"""
        # Mock embedding generator
        mock_embedding_instance = Mock()
        mock_embedding_instance.generate_embeddings.return_value = np.random.rand(1, 384).astype(np.float32)
        mock_embedding_generator.return_value = mock_embedding_instance
        
        # Mock vector store
        mock_vector_store_instance = Mock()
        mock_vector_store_instance.load_index.return_value = True
        mock_vector_store_instance.search.return_value = [
            {
                'chunk_id': 0,
                'filename': 'test.pdf',
                'chunk_text': 'Sample chunk text',
                'chunk_tokens': 10,
                'created_at': '2024-01-01',
                'similarity_score': 0.8
            }
        ]
        mock_vector_store.return_value = mock_vector_store_instance
        
        self.retriever = ContextRetriever()
    
    def test_initialization(self):
        """Test context retriever initialization"""
        assert self.retriever.embedding_generator is not None
        assert self.retriever.vector_store is not None
        assert self.retriever.query_processor is not None
    
    def test_retrieve_context_success(self):
        """Test successful context retrieval"""
        query = "What is machine learning?"
        results = self.retriever.retrieve_context(query, k=5)
        
        assert isinstance(results, list)
        assert len(results) >= 0
        
        if results:
            result = results[0]
            assert 'chunk_id' in result
            assert 'filename' in result
            assert 'chunk_text' in result
            assert 'similarity_score' in result
    
    def test_retrieve_context_with_threshold(self):
        """Test context retrieval with similarity threshold"""
        query = "What is machine learning?"
        results = self.retriever.retrieve_context(
            query, 
            k=5, 
            similarity_threshold=0.5
        )
        
        assert isinstance(results, list)
        # All results should meet the threshold
        for result in results:
            assert result['similarity_score'] >= 0.5
    
    def test_retrieve_context_with_expansion(self):
        """Test context retrieval with query expansion"""
        query = "How does AI work?"
        results = self.retriever.retrieve_context(
            query, 
            k=5, 
            expand_query=True
        )
        
        assert isinstance(results, list)
    
    def test_rerank_results_similarity(self):
        """Test result reranking by similarity"""
        results = [
            {'chunk_id': 1, 'filename': 'test1.pdf', 'similarity_score': 0.7},
            {'chunk_id': 2, 'filename': 'test2.pdf', 'similarity_score': 0.9},
            {'chunk_id': 3, 'filename': 'test3.pdf', 'similarity_score': 0.5}
        ]
        
        reranked = self.retriever.rerank_results("test query", results, 'similarity')
        
        # Should maintain order (already sorted by similarity)
        assert reranked[0]['similarity_score'] >= reranked[1]['similarity_score']
        assert reranked[1]['similarity_score'] >= reranked[2]['similarity_score']
    
    def test_rerank_results_diversity(self):
        """Test result reranking by diversity"""
        results = [
            {'chunk_id': 1, 'filename': 'test1.pdf', 'similarity_score': 0.9},
            {'chunk_id': 2, 'filename': 'test1.pdf', 'similarity_score': 0.8},
            {'chunk_id': 3, 'filename': 'test2.pdf', 'similarity_score': 0.7}
        ]
        
        reranked = self.retriever.rerank_results("test query", results, 'diversity')
        
        # Should prefer diverse sources
        assert isinstance(reranked, list)
        assert len(reranked) <= len(results)
    
    def test_rerank_results_hybrid(self):
        """Test hybrid reranking"""
        results = [
            {'chunk_id': 1, 'filename': 'test1.pdf', 'similarity_score': 0.9},
            {'chunk_id': 2, 'filename': 'test2.pdf', 'similarity_score': 0.8}
        ]
        
        reranked = self.retriever.rerank_results("test query", results, 'hybrid')
        
        # Should add hybrid scores
        for result in reranked:
            assert 'hybrid_score' in result

class TestHybridRetriever:
    """Test hybrid retrieval functionality"""
    
    @patch('retrieval.ContextRetriever')
    def test_initialization_without_bm25(self, mock_context_retriever):
        """Test hybrid retriever initialization without BM25"""
        retriever = HybridRetriever(use_bm25=False)
        
        assert retriever.use_bm25 is False
        assert retriever.bm25_available is False
    
    @patch('retrieval.ContextRetriever')
    def test_retrieve_hybrid_without_bm25(self, mock_context_retriever):
        """Test hybrid retrieval without BM25 (falls back to dense)"""
        # Mock context retriever
        mock_instance = Mock()
        mock_instance.retrieve_context.return_value = [
            {'chunk_id': 1, 'similarity_score': 0.8}
        ]
        mock_context_retriever.return_value = mock_instance
        
        retriever = HybridRetriever(use_bm25=False)
        results = retriever.retrieve_hybrid("test query", k=5)
        
        assert isinstance(results, list)

class TestRAGRetriever:
    """Test main RAG retriever interface"""
    
    @patch('retrieval.ContextRetriever')
    def setup_method(self, mock_context_retriever):
        """Setup with mocked context retriever"""
        # Mock context retriever
        mock_instance = Mock()
        mock_instance.retrieve_context.return_value = [
            {
                'chunk_id': 1,
                'filename': 'test.pdf',
                'chunk_text': 'Sample text about machine learning',
                'similarity_score': 0.8
            }
        ]
        mock_instance.vector_store = Mock()
        mock_instance.vector_store.get_stats.return_value = {
            'total_chunks': 10,
            'total_files': 2,
            'total_tokens': 1000,
            'index_size': 10
        }
        mock_context_retriever.return_value = mock_instance
        
        self.retriever = RAGRetriever(use_hybrid=False)
    
    def test_initialization_dense(self):
        """Test RAG retriever initialization with dense retrieval"""
        assert self.retriever.retrieval_method == "dense"
        assert self.retriever.default_k == 5
    
    @patch('retrieval.HybridRetriever')
    def test_initialization_hybrid(self, mock_hybrid_retriever):
        """Test RAG retriever initialization with hybrid retrieval"""
        retriever = RAGRetriever(use_hybrid=True)
        assert retriever.retrieval_method == "hybrid"
    
    def test_search_success(self):
        """Test successful search"""
        query = "What is machine learning?"
        result = self.retriever.search(query, k=3)
        
        assert result['success'] is True
        assert result['query'] == query
        assert 'num_results' in result
        assert 'results' in result
        assert 'context_text' in result
        assert 'retrieval_method' in result
        assert isinstance(result['results'], list)
    
    def test_search_with_custom_k(self):
        """Test search with custom k value"""
        query = "Test query"
        result = self.retriever.search(query, k=10)
        
        assert result['success'] is True
        assert result['query'] == query
    
    def test_search_with_threshold(self):
        """Test search with similarity threshold"""
        query = "Test query"
        result = self.retriever.search(
            query, 
            k=5, 
            similarity_threshold=0.5
        )
        
        assert result['success'] is True
    
    def test_prepare_context_text(self):
        """Test context text preparation"""
        results = [
            {
                'filename': 'test1.pdf',
                'chunk_text': 'First chunk of text',
                'similarity_score': 0.9
            },
            {
                'filename': 'test2.pdf',
                'chunk_text': 'Second chunk of text',
                'similarity_score': 0.7
            }
        ]
        
        context_text = self.retriever._prepare_context_text(results)
        
        assert isinstance(context_text, str)
        assert 'First chunk of text' in context_text
        assert 'Second chunk of text' in context_text
        assert 'test1.pdf' in context_text
        assert 'test2.pdf' in context_text
        assert '[Context 1]' in context_text
        assert '[Context 2]' in context_text
    
    def test_prepare_context_text_empty(self):
        """Test context text preparation with empty results"""
        context_text = self.retriever._prepare_context_text([])
        assert context_text == ""
    
    def test_get_retrieval_stats(self):
        """Test getting retrieval statistics"""
        stats = self.retriever.get_retrieval_stats()
        
        assert isinstance(stats, dict)
        assert 'total_chunks' in stats
        assert 'total_files' in stats

class TestQuickSearch:
    """Test quick search convenience function"""
    
    @patch('retrieval.RAGRetriever')
    def test_quick_search(self, mock_rag_retriever):
        """Test quick search function"""
        # Mock RAGRetriever
        mock_instance = Mock()
        mock_instance.search.return_value = {
            'success': True,
            'query': 'test',
            'results': []
        }
        mock_rag_retriever.return_value = mock_instance
        
        result = quick_search("test query", k=3)
        
        assert isinstance(result, dict)
        assert 'success' in result
        assert 'query' in result

# Error handling tests
class TestErrorHandling:
    """Test error handling in retrieval components"""
    
    def test_context_retriever_with_invalid_model(self):
        """Test context retriever with invalid embedding model"""
        with pytest.raises(Exception):
            # This should fail when trying to load an invalid model
            ContextRetriever(embedding_model="invalid/model/name")
    
    @patch('retrieval.ContextRetriever')
    def test_rag_retriever_search_error(self, mock_context_retriever):
        """Test RAG retriever error handling during search"""
        # Mock context retriever to raise an exception
        mock_instance = Mock()
        mock_instance.retrieve_context.side_effect = Exception("Test error")
        mock_context_retriever.return_value = mock_instance
        
        retriever = RAGRetriever(use_hybrid=False)
        result = retriever.search("test query")
        
        assert result['success'] is False
        assert 'error' in result

# Performance tests
class TestPerformance:
    """Test performance characteristics"""
    
    @patch('retrieval.ContextRetriever')
    def test_search_performance(self, mock_context_retriever):
        """Test search performance with large result sets"""
        # Mock large result set
        large_results = [
            {
                'chunk_id': i,
                'filename': f'test{i}.pdf',
                'chunk_text': f'Sample text {i}',
                'similarity_score': 0.8 - (i * 0.01)
            }
            for i in range(100)
        ]
        
        mock_instance = Mock()
        mock_instance.retrieve_context.return_value = large_results
        mock_instance.vector_store = Mock()
        mock_instance.vector_store.get_stats.return_value = {}
        mock_context_retriever.return_value = mock_instance
        
        retriever = RAGRetriever(use_hybrid=False)
        
        import time
        start_time = time.time()
        result = retriever.search("test query", k=50)
        end_time = time.time()
        
        # Should complete within reasonable time
        assert (end_time - start_time) < 1.0  # Less than 1 second
        assert result['success'] is True
        assert len(result['results']) <= 50

if __name__ == "__main__":
    pytest.main([__file__])
