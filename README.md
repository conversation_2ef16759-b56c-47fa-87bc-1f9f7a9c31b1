# RAG Pipeline - PDF Chat Assistant

A complete Retrieval-Augmented Generation (RAG) pipeline built with Python, Streamlit, and TogetherAI. This application allows you to upload PDF documents, process them into a searchable knowledge base, and chat with your documents using advanced AI models.

## 🚀 Features

- **PDF Document Ingestion**: Upload and process multiple PDF files
- **Advanced Text Processing**: Multiple extraction and chunking methods
- **Vector Search**: FAISS-based similarity search with sentence transformers
- **AI Chat Interface**: Powered by TogetherAI's Llama models
- **Persistent Storage**: SQLite database and FAISS index persistence
- **Real-time Chat**: Interactive chat interface with context sources
- **System Monitoring**: Real-time statistics and connection status

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PDF Upload    │───▶│   Text Extract  │───▶│   Chunking      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chat UI       │◀───│   TogetherAI    │◀───│   Retrieval     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SQLite DB     │◀───│   FAISS Index   │◀───│   Embeddings    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

- Python 3.8 or higher
- TogetherAI API key (free tier available)
- 4GB+ RAM (for embedding models)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd rag-pipeline
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your TogetherAI API key:
   ```
   TOGETHERAI_API_KEY=your_api_key_here
   ```

4. **Get TogetherAI API Key**:
   - Visit [TogetherAI](https://together.ai)
   - Sign up for a free account
   - Navigate to API Keys section
   - Create a new API key
   - Copy the key to your `.env` file

## 🚀 Quick Start

1. **Run the application**:
   ```bash
   streamlit run app.py
   ```

2. **Open your browser** to `http://localhost:8501`

3. **Upload PDF documents**:
   - Go to the "Document Ingestion" tab
   - Upload one or more PDF files
   - Configure extraction and chunking settings
   - Click "Ingest Documents"

4. **Start chatting**:
   - Switch to the "Chat Interface" tab
   - Ask questions about your uploaded documents
   - View context sources for each response

## 📁 Project Structure

```
rag-pipeline/
├── app.py                 # Main Streamlit application
├── ingestion.py          # PDF processing and embedding generation
├── retrieval.py          # Vector search and context retrieval
├── chat_completion.py    # TogetherAI integration and chat logic
├── requirements.txt      # Python dependencies
├── .env                  # Environment variables (create from .env.example)
├── .env.example         # Environment variables template
├── README.md            # This file
├── tests/               # Test files
│   ├── test_ingestion.py
│   ├── test_retrieval.py
│   └── test_chat.py
├── faiss_index.index    # FAISS vector index (created automatically)
└── metadata.db          # SQLite database (created automatically)
```

## 🔧 Configuration

### Embedding Models

The system uses `sentence-transformers/all-MiniLM-L6-v2` by default. You can change this in the code:

```python
# In ingestion.py and retrieval.py
embedding_model = "sentence-transformers/all-MiniLM-L6-v2"
```

### Chunking Parameters

Adjust text chunking in the Streamlit interface or modify defaults:

```python
chunk_size = 1000      # Characters per chunk
chunk_overlap = 200    # Overlap between chunks
```

### TogetherAI Models

Default model: `meta-llama/Llama-3.3-70B-Instruct-Turbo-Free`

Available models:
- `meta-llama/Llama-3.3-70B-Instruct-Turbo-Free` (Free tier)
- `meta-llama/Llama-3.1-8B-Instruct-Turbo` (Paid)
- `mistralai/Mixtral-8x7B-Instruct-v0.1` (Paid)

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_ingestion.py -v

# Run with coverage
pytest tests/ --cov=. --cov-report=html
```

## 📊 Performance

### Benchmarks

- **PDF Processing**: ~1-2 seconds per page
- **Embedding Generation**: ~100 chunks per second
- **Vector Search**: <100ms for 10k chunks
- **Response Generation**: 2-5 seconds (depends on model)

### Memory Usage

- **Base Application**: ~500MB
- **Embedding Model**: ~400MB
- **Per 1000 chunks**: ~50MB

## 🔍 API Reference

### DocumentIngestion

```python
from ingestion import DocumentIngestion

# Initialize
ingestion = DocumentIngestion(
    chunk_size=1000,
    chunk_overlap=200,
    embedding_model="sentence-transformers/all-MiniLM-L6-v2"
)

# Ingest PDF
result = ingestion.ingest_pdf(
    pdf_path="document.pdf",
    extraction_method="pdfplumber",
    chunking_method="recursive"
)
```

### RAGRetriever

```python
from retrieval import RAGRetriever

# Initialize
retriever = RAGRetriever(
    embedding_model="sentence-transformers/all-MiniLM-L6-v2",
    use_hybrid=False,
    default_k=5
)

# Search
results = retriever.search(
    query="What is machine learning?",
    k=5,
    similarity_threshold=0.0
)
```

### RAGChatbot

```python
from chat_completion import RAGChatbot

# Initialize
chatbot = RAGChatbot()

# Generate response
response = chatbot.generate_rag_response(
    user_query="Explain the concept",
    context="Retrieved context here...",
    max_tokens=1000,
    temperature=0.7
)
```

## 🚨 Troubleshooting

### Common Issues

1. **API Connection Failed**:
   - Check your TogetherAI API key
   - Verify internet connection
   - Check API rate limits

2. **PDF Processing Errors**:
   - Try different extraction methods (pdfplumber vs pypdf2)
   - Check if PDF is text-based (not scanned images)
   - Ensure PDF is not password-protected

3. **Memory Issues**:
   - Reduce chunk size
   - Process fewer documents at once
   - Use a smaller embedding model

4. **Slow Performance**:
   - Reduce number of context chunks
   - Use GPU acceleration if available
   - Optimize chunk size and overlap

### Error Messages

- `"No text could be extracted from PDF"`: Try different extraction method
- `"TogetherAI API not connected"`: Check API key and internet connection
- `"Embedding model not loaded"`: Restart application, check memory
- `"No existing vector index found"`: Upload and ingest documents first

## 🔒 Security

- API keys are stored in environment variables
- No data is sent to external services except TogetherAI
- All processing happens locally
- SQLite database is local only

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [TogetherAI](https://together.ai) for the API
- [Sentence Transformers](https://www.sbert.net/) for embeddings
- [FAISS](https://faiss.ai/) for vector search
- [Streamlit](https://streamlit.io/) for the web interface
- [LangChain](https://langchain.com/) for text processing utilities

## 📞 Support

For support, please:
1. Check the troubleshooting section
2. Search existing issues
3. Create a new issue with detailed information
4. Include error messages and system information

---

**Happy chatting with your documents! 📚🤖**
