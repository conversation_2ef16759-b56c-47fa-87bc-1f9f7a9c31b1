"""
Retrieval Module for RAG Pipeline
Handles similarity search, context retrieval, and query processing
"""

import os
import logging
from typing import List, Dict, Optional, Tuple
import numpy as np
from sentence_transformers import SentenceTransformer
from ingestion import VectorStore, EmbeddingGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QueryProcessor:
    """Processes and enhances user queries"""
    
    def __init__(self):
        pass
    
    def preprocess_query(self, query: str) -> str:
        """Clean and preprocess the user query"""
        # Basic preprocessing
        query = query.strip()
        
        # Remove extra whitespace
        query = ' '.join(query.split())
        
        return query
    
    def expand_query(self, query: str) -> List[str]:
        """Expand query with synonyms or related terms (basic implementation)"""
        # This is a simple implementation - could be enhanced with word embeddings
        expanded_queries = [query]
        
        # Add some basic expansions
        if "how" in query.lower():
            expanded_queries.append(query.replace("how", "what is the method"))
        
        if "what" in query.lower():
            expanded_queries.append(query.replace("what", "explain"))
        
        return expanded_queries

class ContextRetriever:
    """Handles context retrieval and ranking"""
    
    def __init__(self, 
                 embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2",
                 vector_store_path: str = "faiss_index",
                 db_path: str = "metadata.db"):
        
        self.embedding_generator = EmbeddingGenerator(embedding_model)
        self.vector_store = VectorStore(vector_store_path, db_path)
        self.query_processor = QueryProcessor()
        
        # Load existing index
        if not self.vector_store.load_index():
            logger.warning("No existing vector index found. Please ingest documents first.")
    
    def retrieve_context(self, 
                        query: str, 
                        k: int = 5, 
                        similarity_threshold: float = 0.0,
                        expand_query: bool = False) -> List[Dict]:
        """
        Retrieve relevant context for a query
        
        Args:
            query: User query
            k: Number of chunks to retrieve
            similarity_threshold: Minimum similarity score
            expand_query: Whether to expand the query
        
        Returns:
            List of relevant chunks with metadata
        """
        try:
            # Preprocess query
            processed_query = self.query_processor.preprocess_query(query)
            
            # Expand query if requested
            queries = [processed_query]
            if expand_query:
                queries = self.query_processor.expand_query(processed_query)
            
            all_results = []
            
            # Search for each query variant
            for q in queries:
                # Generate query embedding
                query_embedding = self.embedding_generator.generate_embeddings([q])
                
                # Search vector store
                results = self.vector_store.search(query_embedding, k)
                
                # Filter by similarity threshold
                filtered_results = [
                    r for r in results 
                    if r['similarity_score'] >= similarity_threshold
                ]
                
                all_results.extend(filtered_results)
            
            # Remove duplicates and sort by similarity
            unique_results = {}
            for result in all_results:
                chunk_id = result['chunk_id']
                if chunk_id not in unique_results or result['similarity_score'] > unique_results[chunk_id]['similarity_score']:
                    unique_results[chunk_id] = result
            
            # Sort by similarity score (descending)
            final_results = sorted(
                unique_results.values(), 
                key=lambda x: x['similarity_score'], 
                reverse=True
            )[:k]
            
            logger.info(f"Retrieved {len(final_results)} relevant chunks for query: {query[:50]}...")
            return final_results
            
        except Exception as e:
            logger.error(f"Error retrieving context: {e}")
            return []
    
    def get_context_window(self, 
                          chunk_ids: List[int], 
                          window_size: int = 1) -> List[Dict]:
        """
        Get surrounding context for given chunk IDs
        
        Args:
            chunk_ids: List of chunk IDs
            window_size: Number of chunks before/after to include
        
        Returns:
            Extended context with surrounding chunks
        """
        # This is a simplified implementation
        # In a full implementation, you'd query the database for adjacent chunks
        return []
    
    def rerank_results(self, 
                      query: str, 
                      results: List[Dict], 
                      method: str = 'similarity') -> List[Dict]:
        """
        Rerank results using different strategies
        
        Args:
            query: Original query
            results: Initial results
            method: Reranking method ('similarity', 'diversity', 'hybrid')
        
        Returns:
            Reranked results
        """
        if method == 'similarity':
            # Already sorted by similarity
            return results
        
        elif method == 'diversity':
            # Simple diversity reranking - select diverse chunks
            diverse_results = []
            used_files = set()
            
            for result in results:
                if result['filename'] not in used_files or len(diverse_results) < 2:
                    diverse_results.append(result)
                    used_files.add(result['filename'])
                
                if len(diverse_results) >= len(results):
                    break
            
            return diverse_results
        
        elif method == 'hybrid':
            # Combine similarity and diversity
            # This is a simple implementation
            similarity_weight = 0.7
            diversity_weight = 0.3
            
            # Calculate diversity scores
            for i, result in enumerate(results):
                diversity_score = 1.0 / (1.0 + i)  # Simple position-based diversity
                result['hybrid_score'] = (
                    similarity_weight * result['similarity_score'] + 
                    diversity_weight * diversity_score
                )
            
            return sorted(results, key=lambda x: x['hybrid_score'], reverse=True)
        
        else:
            return results

class HybridRetriever:
    """Combines dense and sparse retrieval methods"""
    
    def __init__(self, 
                 embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2",
                 use_bm25: bool = False):
        
        self.context_retriever = ContextRetriever(embedding_model)
        self.use_bm25 = use_bm25
        
        if use_bm25:
            try:
                from whoosh.index import create_index, open_dir
                from whoosh.fields import Schema, TEXT, ID
                from whoosh.qparser import QueryParser
                self.bm25_available = True
                logger.info("BM25 retrieval enabled")
            except ImportError:
                logger.warning("Whoosh not available. BM25 retrieval disabled.")
                self.bm25_available = False
                self.use_bm25 = False
    
    def retrieve_hybrid(self, 
                       query: str, 
                       k: int = 5,
                       dense_weight: float = 0.7,
                       sparse_weight: float = 0.3) -> List[Dict]:
        """
        Hybrid retrieval combining dense and sparse methods
        
        Args:
            query: User query
            k: Number of results to return
            dense_weight: Weight for dense retrieval
            sparse_weight: Weight for sparse retrieval
        
        Returns:
            Combined and reranked results
        """
        # Dense retrieval
        dense_results = self.context_retriever.retrieve_context(query, k * 2)
        
        if not self.use_bm25 or not self.bm25_available:
            return dense_results[:k]
        
        # TODO: Implement BM25 retrieval
        # For now, return dense results
        return dense_results[:k]

class RAGRetriever:
    """Main retrieval interface for the RAG pipeline"""
    
    def __init__(self, 
                 embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2",
                 use_hybrid: bool = False,
                 default_k: int = 5):
        
        self.default_k = default_k
        
        if use_hybrid:
            self.retriever = HybridRetriever(embedding_model, use_bm25=True)
            self.retrieval_method = "hybrid"
        else:
            self.retriever = ContextRetriever(embedding_model)
            self.retrieval_method = "dense"
        
        logger.info(f"RAG Retriever initialized with {self.retrieval_method} retrieval")
    
    def search(self, 
               query: str, 
               k: Optional[int] = None,
               similarity_threshold: float = 0.0,
               rerank_method: str = 'similarity') -> Dict:
        """
        Main search interface
        
        Args:
            query: User query
            k: Number of results (uses default if None)
            similarity_threshold: Minimum similarity score
            rerank_method: Reranking strategy
        
        Returns:
            Search results with metadata
        """
        if k is None:
            k = self.default_k
        
        try:
            # Retrieve context
            if self.retrieval_method == "hybrid":
                results = self.retriever.retrieve_hybrid(query, k)
            else:
                results = self.retriever.retrieve_context(
                    query, k, similarity_threshold
                )
            
            # Rerank if needed
            if rerank_method != 'similarity' and hasattr(self.retriever, 'rerank_results'):
                results = self.retriever.rerank_results(query, results, rerank_method)
            
            # Prepare context for LLM
            context_text = self._prepare_context_text(results)
            
            return {
                'success': True,
                'query': query,
                'num_results': len(results),
                'results': results,
                'context_text': context_text,
                'retrieval_method': self.retrieval_method
            }
            
        except Exception as e:
            logger.error(f"Error in search: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': query,
                'results': [],
                'context_text': ""
            }
    
    def _prepare_context_text(self, results: List[Dict]) -> str:
        """Prepare context text for LLM input"""
        if not results:
            return ""
        
        context_parts = []
        for i, result in enumerate(results, 1):
            context_parts.append(
                f"[Context {i}] (Source: {result['filename']}, "
                f"Similarity: {result['similarity_score']:.3f})\n"
                f"{result['chunk_text']}\n"
            )
        
        return "\n".join(context_parts)
    
    def get_retrieval_stats(self) -> Dict:
        """Get retrieval system statistics"""
        if hasattr(self.retriever, 'vector_store'):
            return self.retriever.vector_store.get_stats()
        elif hasattr(self.retriever, 'context_retriever'):
            return self.retriever.context_retriever.vector_store.get_stats()
        else:
            return {}

# Convenience function for quick retrieval
def quick_search(query: str, k: int = 5) -> Dict:
    """Quick search function for simple use cases"""
    retriever = RAGRetriever()
    return retriever.search(query, k)
