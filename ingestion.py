"""
PDF Ingestion and Processing Module for RAG Pipeline
Handles PDF loading, text extraction, chunking, embedding, and indexing
"""

import os
import sqlite3
import hashlib
from typing import List, Dict, Tuple, Optional
import logging
from pathlib import Path

import PyPDF2
import pdfplumber
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
from langchain.text_splitter import CharacterTextSplitter, RecursiveCharacterTextSplitter
import tiktoken

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFProcessor:
    """Handles PDF loading and text extraction"""
    
    def __init__(self):
        self.supported_methods = ['pypdf2', 'pdfplumber']
    
    def extract_text_pypdf2(self, pdf_path: str) -> str:
        """Extract text using PyPDF2"""
        try:
            text = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += f"\n--- Page {page_num + 1} ---\n{page_text}"
                    except Exception as e:
                        logger.warning(f"Error extracting page {page_num + 1}: {e}")
                        continue
            return text
        except Exception as e:
            logger.error(f"Error with PyPDF2 extraction: {e}")
            return ""
    
    def extract_text_pdfplumber(self, pdf_path: str) -> str:
        """Extract text using pdfplumber (better for complex layouts)"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += f"\n--- Page {page_num + 1} ---\n{page_text}"
                    except Exception as e:
                        logger.warning(f"Error extracting page {page_num + 1}: {e}")
                        continue
            return text
        except Exception as e:
            logger.error(f"Error with pdfplumber extraction: {e}")
            return ""
    
    def extract_text(self, pdf_path: str, method: str = 'pdfplumber') -> str:
        """Extract text from PDF using specified method"""
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        if method == 'pypdf2':
            text = self.extract_text_pypdf2(pdf_path)
        elif method == 'pdfplumber':
            text = self.extract_text_pdfplumber(pdf_path)
        else:
            raise ValueError(f"Unsupported extraction method: {method}")
        
        if not text.strip():
            logger.warning("No text extracted from PDF. Trying alternative method...")
            # Try alternative method if first one fails
            alt_method = 'pypdf2' if method == 'pdfplumber' else 'pdfplumber'
            text = self.extract_text(pdf_path, alt_method)
        
        return text.strip()

class TextChunker:
    """Handles text chunking with various strategies"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.encoding.encode(text))
    
    def chunk_by_character(self, text: str) -> List[str]:
        """Chunk text by character count"""
        splitter = CharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            separator="\n\n"
        )
        return splitter.split_text(text)
    
    def chunk_recursive(self, text: str) -> List[str]:
        """Chunk text using recursive character splitter"""
        splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            separators=["\n\n", "\n", ". ", " ", ""]
        )
        return splitter.split_text(text)
    
    def chunk_by_tokens(self, text: str, max_tokens: int = None) -> List[str]:
        """Chunk text by token count"""
        if max_tokens is None:
            max_tokens = self.chunk_size
        
        tokens = self.encoding.encode(text)
        chunks = []
        
        for i in range(0, len(tokens), max_tokens - self.chunk_overlap):
            chunk_tokens = tokens[i:i + max_tokens]
            chunk_text = self.encoding.decode(chunk_tokens)
            chunks.append(chunk_text)
        
        return chunks
    
    def chunk_text(self, text: str, method: str = 'recursive') -> List[str]:
        """Chunk text using specified method"""
        if method == 'character':
            return self.chunk_by_character(text)
        elif method == 'recursive':
            return self.chunk_recursive(text)
        elif method == 'tokens':
            return self.chunk_by_tokens(text)
        else:
            raise ValueError(f"Unsupported chunking method: {method}")

class EmbeddingGenerator:
    """Generates embeddings using sentence-transformers"""
    
    def __init__(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2"):
        self.model_name = model_name
        self.model = None
        self.embedding_dim = None
        self._load_model()
    
    def _load_model(self):
        """Load the embedding model"""
        try:
            logger.info(f"Loading embedding model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            # Get embedding dimension
            test_embedding = self.model.encode(["test"])
            self.embedding_dim = test_embedding.shape[1]
            logger.info(f"Model loaded successfully. Embedding dimension: {self.embedding_dim}")
        except Exception as e:
            logger.error(f"Error loading embedding model: {e}")
            raise
    
    def generate_embeddings(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """Generate embeddings for a list of texts"""
        if not self.model:
            raise RuntimeError("Embedding model not loaded")
        
        try:
            logger.info(f"Generating embeddings for {len(texts)} texts...")
            embeddings = self.model.encode(
                texts, 
                batch_size=batch_size,
                show_progress_bar=True,
                convert_to_numpy=True
            )
            logger.info(f"Generated embeddings shape: {embeddings.shape}")
            return embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise

class VectorStore:
    """Manages FAISS vector index and SQLite metadata"""
    
    def __init__(self, index_path: str = "faiss_index", db_path: str = "metadata.db"):
        self.index_path = index_path
        self.db_path = db_path
        self.index = None
        self.embedding_dim = None
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for metadata"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                file_hash TEXT NOT NULL,
                chunk_id INTEGER NOT NULL,
                chunk_text TEXT NOT NULL,
                chunk_tokens INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(file_hash, chunk_id)
            )
        ''')
        conn.commit()
        conn.close()
    
    def _get_file_hash(self, file_path: str) -> str:
        """Generate hash for file content"""
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    
    def create_index(self, embedding_dim: int):
        """Create new FAISS index"""
        self.embedding_dim = embedding_dim
        self.index = faiss.IndexFlatIP(embedding_dim)  # Inner product for cosine similarity
        logger.info(f"Created new FAISS index with dimension {embedding_dim}")
    
    def load_index(self) -> bool:
        """Load existing FAISS index"""
        if os.path.exists(f"{self.index_path}.index"):
            try:
                self.index = faiss.read_index(f"{self.index_path}.index")
                self.embedding_dim = self.index.d
                logger.info(f"Loaded existing FAISS index with dimension {self.embedding_dim}")
                return True
            except Exception as e:
                logger.error(f"Error loading FAISS index: {e}")
                return False
        return False
    
    def save_index(self):
        """Save FAISS index to disk"""
        if self.index:
            faiss.write_index(self.index, f"{self.index_path}.index")
            logger.info("FAISS index saved successfully")
    
    def add_documents(self, file_path: str, chunks: List[str], embeddings: np.ndarray):
        """Add documents to vector store and metadata database"""
        if not self.index:
            self.create_index(embeddings.shape[1])
        
        file_hash = self._get_file_hash(file_path)
        filename = os.path.basename(file_path)
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        
        # Add to FAISS index
        start_id = self.index.ntotal
        self.index.add(embeddings)
        
        # Add metadata to SQLite
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for i, chunk in enumerate(chunks):
            chunk_tokens = len(chunk.split())  # Simple token count
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO documents 
                    (filename, file_hash, chunk_id, chunk_text, chunk_tokens)
                    VALUES (?, ?, ?, ?, ?)
                ''', (filename, file_hash, start_id + i, chunk, chunk_tokens))
            except sqlite3.IntegrityError:
                logger.warning(f"Duplicate chunk detected for {filename}, chunk {i}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"Added {len(chunks)} chunks from {filename} to vector store")
        return start_id, start_id + len(chunks) - 1
    
    def search(self, query_embedding: np.ndarray, k: int = 5) -> List[Dict]:
        """Search for similar chunks"""
        if not self.index:
            return []
        
        # Normalize query embedding
        query_embedding = query_embedding.reshape(1, -1)
        faiss.normalize_L2(query_embedding)
        
        # Search FAISS index
        scores, indices = self.index.search(query_embedding, k)
        
        # Get metadata from SQLite
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx == -1:  # FAISS returns -1 for empty results
                continue
            
            cursor.execute('''
                SELECT filename, chunk_text, chunk_tokens, created_at
                FROM documents WHERE chunk_id = ?
            ''', (int(idx),))
            
            row = cursor.fetchone()
            if row:
                results.append({
                    'chunk_id': int(idx),
                    'filename': row[0],
                    'chunk_text': row[1],
                    'chunk_tokens': row[2],
                    'created_at': row[3],
                    'similarity_score': float(score)
                })
        
        conn.close()
        return results
    
    def get_stats(self) -> Dict:
        """Get statistics about the vector store"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM documents')
        total_chunks = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT filename) FROM documents')
        total_files = cursor.fetchone()[0]
        
        cursor.execute('SELECT SUM(chunk_tokens) FROM documents')
        total_tokens = cursor.fetchone()[0] or 0
        
        conn.close()
        
        return {
            'total_chunks': total_chunks,
            'total_files': total_files,
            'total_tokens': total_tokens,
            'index_size': self.index.ntotal if self.index else 0
        }

    def get_chunks_by_filename(self, filename: str) -> List[Dict]:
        """Get all chunks for a specific filename"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT chunk_id, chunk_text, chunk_tokens, created_at
            FROM documents
            WHERE filename = ?
            ORDER BY chunk_id
        ''', (filename,))

        results = []
        for row in cursor.fetchall():
            results.append({
                'chunk_id': row[0],
                'text': row[1],
                'tokens': row[2],
                'created_at': row[3],
                'filename': filename
            })

        conn.close()
        return results

    def clear_index(self):
        """Clear the entire vector store"""
        # Remove FAISS index file
        if os.path.exists(f"{self.index_path}.index"):
            os.remove(f"{self.index_path}.index")
        
        # Clear SQLite database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('DELETE FROM documents')
        conn.commit()
        conn.close()
        
        # Reset index
        self.index = None
        self.embedding_dim = None
        
        logger.info("Vector store cleared successfully")

class DocumentIngestion:
    """Main class for document ingestion pipeline"""
    
    def __init__(self, 
                 chunk_size: int = 1000,
                 chunk_overlap: int = 200,
                 embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"):
        
        self.pdf_processor = PDFProcessor()
        self.text_chunker = TextChunker(chunk_size, chunk_overlap)
        self.embedding_generator = EmbeddingGenerator(embedding_model)
        self.vector_store = VectorStore()
        
        # Load existing index if available
        self.vector_store.load_index()
    
    def ingest_pdf(self, pdf_path: str, extraction_method: str = 'pdfplumber', 
                   chunking_method: str = 'recursive') -> Dict:
        """Complete PDF ingestion pipeline"""
        try:
            logger.info(f"Starting ingestion of {pdf_path}")
            
            # Extract text
            text = self.pdf_processor.extract_text(pdf_path, extraction_method)
            if not text:
                raise ValueError("No text could be extracted from PDF")
            
            # Chunk text
            chunks = self.text_chunker.chunk_text(text, chunking_method)
            if not chunks:
                raise ValueError("No chunks created from text")
            
            # Generate embeddings
            embeddings = self.embedding_generator.generate_embeddings(chunks)
            
            # Add to vector store
            start_id, end_id = self.vector_store.add_documents(pdf_path, chunks, embeddings)
            
            # Save index
            self.vector_store.save_index()
            
            result = {
                'success': True,
                'filename': os.path.basename(pdf_path),
                'chunks_created': len(chunks),
                'chunk_ids': (start_id, end_id),
                'total_characters': len(text),
                'extraction_method': extraction_method,
                'chunking_method': chunking_method
            }
            
            logger.info(f"Successfully ingested {pdf_path}: {len(chunks)} chunks created")
            return result
            
        except Exception as e:
            logger.error(f"Error ingesting {pdf_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'filename': os.path.basename(pdf_path)
            }
    
    def get_vector_store_stats(self) -> Dict:
        """Get vector store statistics"""
        return self.vector_store.get_stats()
    
    def clear_all_data(self):
        """Clear all ingested data"""
        self.vector_store.clear_index()
