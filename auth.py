"""
Authentication module for RAG Pipeline
Provides simple user authentication and session management
"""

import streamlit as st
import hashlib
import sqlite3
import os
from typing import Dict, Optional, Tuple
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserManager:
    """Manages user authentication and sessions"""
    
    def __init__(self, db_path: str = "users.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Initialize user database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                session_token TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user if no users exist
        self._create_default_admin()
    
    def _create_default_admin(self):
        """Create default admin user if no users exist"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM users')
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            admin_password = "admin123"  # Change this in production!
            password_hash = self._hash_password(admin_password)
            
            cursor.execute('''
                INSERT INTO users (username, password_hash, email)
                VALUES (?, ?, ?)
            ''', ("admin", password_hash, "<EMAIL>"))
            
            conn.commit()
            logger.info("Default admin user created (username: admin, password: admin123)")
        
        conn.close()
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_user(self, username: str, password: str, email: str = None) -> bool:
        """Create a new user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            password_hash = self._hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (username, password_hash, email)
                VALUES (?, ?, ?)
            ''', (username, password_hash, email))
            
            conn.commit()
            conn.close()
            
            logger.info(f"User created: {username}")
            return True
            
        except sqlite3.IntegrityError:
            logger.warning(f"User creation failed: {username} already exists")
            return False
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return False
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """Authenticate user and return user info"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            password_hash = self._hash_password(password)
            
            cursor.execute('''
                SELECT id, username, email, is_active
                FROM users
                WHERE username = ? AND password_hash = ? AND is_active = 1
            ''', (username, password_hash))
            
            user = cursor.fetchone()
            
            if user:
                # Update last login
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (user[0],))
                conn.commit()
                
                user_info = {
                    'id': user[0],
                    'username': user[1],
                    'email': user[2],
                    'is_active': user[3]
                }
                
                logger.info(f"User authenticated: {username}")
                conn.close()
                return user_info
            
            conn.close()
            return None
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    def create_session(self, user_id: int, duration_hours: int = 24) -> str:
        """Create a new session for user"""
        try:
            import secrets
            session_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(hours=duration_hours)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO sessions (user_id, session_token, expires_at)
                VALUES (?, ?, ?)
            ''', (user_id, session_token, expires_at))
            
            conn.commit()
            conn.close()
            
            return session_token
            
        except Exception as e:
            logger.error(f"Session creation error: {e}")
            return None
    
    def validate_session(self, session_token: str) -> Optional[Dict]:
        """Validate session and return user info"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT u.id, u.username, u.email, s.expires_at
                FROM sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.session_token = ? AND s.is_active = 1 AND u.is_active = 1
            ''', (session_token,))
            
            result = cursor.fetchone()
            
            if result:
                user_id, username, email, expires_at = result
                expires_datetime = datetime.fromisoformat(expires_at)
                
                if expires_datetime > datetime.now():
                    conn.close()
                    return {
                        'id': user_id,
                        'username': username,
                        'email': email
                    }
                else:
                    # Session expired, deactivate it
                    cursor.execute('''
                        UPDATE sessions SET is_active = 0
                        WHERE session_token = ?
                    ''', (session_token,))
                    conn.commit()
            
            conn.close()
            return None
            
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return None
    
    def logout_session(self, session_token: str) -> bool:
        """Logout session"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE sessions SET is_active = 0
                WHERE session_token = ?
            ''', (session_token,))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return False

class StreamlitAuth:
    """Streamlit authentication wrapper"""
    
    def __init__(self):
        self.user_manager = UserManager()
        
        # Initialize session state
        if 'authenticated' not in st.session_state:
            st.session_state.authenticated = False
        if 'user_info' not in st.session_state:
            st.session_state.user_info = None
        if 'session_token' not in st.session_state:
            st.session_state.session_token = None
    
    def login_form(self) -> bool:
        """Display login form and handle authentication"""
        st.markdown("### 🔐 Login Required")
        
        with st.form("login_form"):
            username = st.text_input("Username")
            password = st.text_input("Password", type="password")
            submitted = st.form_submit_button("Login")
            
            if submitted:
                if username and password:
                    user_info = self.user_manager.authenticate_user(username, password)
                    
                    if user_info:
                        # Create session
                        session_token = self.user_manager.create_session(user_info['id'])
                        
                        if session_token:
                            st.session_state.authenticated = True
                            st.session_state.user_info = user_info
                            st.session_state.session_token = session_token
                            st.success(f"Welcome, {user_info['username']}!")
                            st.rerun()
                        else:
                            st.error("Session creation failed")
                    else:
                        st.error("Invalid username or password")
                else:
                    st.error("Please enter both username and password")
        
        # Registration form
        with st.expander("📝 Create New Account"):
            with st.form("register_form"):
                new_username = st.text_input("New Username")
                new_email = st.text_input("Email (optional)")
                new_password = st.text_input("New Password", type="password")
                confirm_password = st.text_input("Confirm Password", type="password")
                register_submitted = st.form_submit_button("Register")
                
                if register_submitted:
                    if new_username and new_password:
                        if new_password == confirm_password:
                            if self.user_manager.create_user(new_username, new_password, new_email):
                                st.success("Account created successfully! Please login.")
                            else:
                                st.error("Username already exists")
                        else:
                            st.error("Passwords do not match")
                    else:
                        st.error("Please enter username and password")
        
        return st.session_state.authenticated
    
    def check_authentication(self) -> bool:
        """Check if user is authenticated"""
        # Check session token if exists
        if st.session_state.session_token:
            user_info = self.user_manager.validate_session(st.session_state.session_token)
            
            if user_info:
                st.session_state.authenticated = True
                st.session_state.user_info = user_info
                return True
            else:
                # Session invalid, clear state
                st.session_state.authenticated = False
                st.session_state.user_info = None
                st.session_state.session_token = None
        
        return st.session_state.authenticated
    
    def logout(self):
        """Logout current user"""
        if st.session_state.session_token:
            self.user_manager.logout_session(st.session_state.session_token)
        
        st.session_state.authenticated = False
        st.session_state.user_info = None
        st.session_state.session_token = None
        st.rerun()
    
    def get_user_info(self) -> Optional[Dict]:
        """Get current user information"""
        return st.session_state.user_info
    
    def require_auth(self, func):
        """Decorator to require authentication for a function"""
        def wrapper(*args, **kwargs):
            if self.check_authentication():
                return func(*args, **kwargs)
            else:
                self.login_form()
                return None
        return wrapper

# Convenience function for easy integration
def require_authentication():
    """Simple function to add authentication to Streamlit app"""
    auth = StreamlitAuth()
    
    if not auth.check_authentication():
        return auth.login_form()
    
    # Add logout button to sidebar
    with st.sidebar:
        user_info = auth.get_user_info()
        if user_info:
            st.write(f"👤 Logged in as: **{user_info['username']}**")
            if st.button("🚪 Logout"):
                auth.logout()
    
    return True
